import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Spinner, <PERSON><PERSON>, Badge } from 'react-bootstrap';
import { Filter, MapPin, Eye, X } from 'lucide-react';
import { 
  getAvailableBoundaryValues, 
  debouncedGetFilteredBoundaryFeatures,
  BoundaryFilters,
  BoundaryFilterResponse,
  FilterOption
} from '../../services/unifiedBoundaryService';

interface InteractiveBoundaryFilterProps {
  onRegionSelection: (regions: GeoJSON.Feature[]) => void;
  onHighlightFeatures: (features: GeoJSON.Feature[]) => void;
  isVisible?: boolean;
}

interface FilterState {
  province?: string;
  district?: string;
  municipality?: string;
  ward?: string;
}

interface DropdownState {
  provinces: FilterOption[];
  districts: FilterOption[];
  municipalities: FilterOption[];
  wards: FilterOption[];
}

interface LoadingState {
  provinces: boolean;
  districts: boolean;
  municipalities: boolean;
  wards: boolean;
  features: boolean;
}

const InteractiveBoundaryFilter: React.FC<InteractiveBoundaryFilterProps> = ({
  onRegionSelection,
  onHighlightFeatures,
  isVisible = true
}) => {
  const [filters, setFilters] = useState<FilterState>({});
  const [dropdownOptions, setDropdownOptions] = useState<DropdownState>({
    provinces: [],
    districts: [],
    municipalities: [],
    wards: []
  });
  const [loading, setLoading] = useState<LoadingState>({
    provinces: false,
    districts: false,
    municipalities: false,
    wards: false,
    features: false
  });
  const [error, setError] = useState<string | null>(null);
  const [filteredFeatures, setFilteredFeatures] = useState<BoundaryFilterResponse | null>(null);

  // Load provinces on component mount
  useEffect(() => {
    loadProvinceOptions();
  }, []);

  // Load cascade options when parent filters change
  useEffect(() => {
    if (filters.province) {
      loadDistrictOptions();
    } else {
      setDropdownOptions(prev => ({ ...prev, districts: [], municipalities: [], wards: [] }));
      setFilters(prev => ({ province: prev.province })); // Clear child filters
    }
  }, [filters.province]);

  useEffect(() => {
    if (filters.district) {
      loadMunicipalityOptions();
    } else {
      setDropdownOptions(prev => ({ ...prev, municipalities: [], wards: [] }));
      setFilters(prev => ({ province: prev.province, district: prev.district })); // Clear child filters
    }
  }, [filters.district]);

  useEffect(() => {
    if (filters.municipality) {
      loadWardOptions();
    } else {
      setDropdownOptions(prev => ({ ...prev, wards: [] }));
      setFilters(prev => ({ province: prev.province, district: prev.district, municipality: prev.municipality })); // Clear child filters
    }
  }, [filters.municipality]);

  // Debounced feature loading when filters change
  useEffect(() => {
    if (Object.keys(filters).length > 0 && Object.values(filters).some(v => v)) {
      setLoading(prev => ({ ...prev, features: true }));
      debouncedGetFilteredBoundaryFeatures(
        filters as BoundaryFilters,
        (result: BoundaryFilterResponse) => {
          setFilteredFeatures(result);
          setLoading(prev => ({ ...prev, features: false }));
          
          // Auto-highlight features for real-time map updates
          onHighlightFeatures(result.features);
        }
      );
    } else {
      setFilteredFeatures(null);
      onHighlightFeatures([]);
    }
  }, [filters, onHighlightFeatures]);

  const loadProvinceOptions = async () => {
    setLoading(prev => ({ ...prev, provinces: true }));
    setError(null);
    try {
      const options = await getAvailableBoundaryValues('provinces');
      setDropdownOptions(prev => ({ ...prev, provinces: options }));
    } catch (err) {
      setError('Failed to load provinces');
      console.error('Province loading error:', err);
    } finally {
      setLoading(prev => ({ ...prev, provinces: false }));
    }
  };

  const loadDistrictOptions = async () => {
    if (!filters.province) return;
    
    setLoading(prev => ({ ...prev, districts: true }));
    try {
      const options = await getAvailableBoundaryValues('districts', { province: filters.province });
      setDropdownOptions(prev => ({ ...prev, districts: options }));
    } catch (err) {
      setError('Failed to load districts');
      console.error('District loading error:', err);
    } finally {
      setLoading(prev => ({ ...prev, districts: false }));
    }
  };

  const loadMunicipalityOptions = async () => {
    if (!filters.province || !filters.district) return;
    
    setLoading(prev => ({ ...prev, municipalities: true }));
    try {
      const options = await getAvailableBoundaryValues('municipalities', { 
        province: filters.province, 
        district: filters.district 
      });
      setDropdownOptions(prev => ({ ...prev, municipalities: options }));
    } catch (err) {
      setError('Failed to load municipalities');
      console.error('Municipality loading error:', err);
    } finally {
      setLoading(prev => ({ ...prev, municipalities: false }));
    }
  };

  const loadWardOptions = async () => {
    if (!filters.province || !filters.district || !filters.municipality) return;
    
    setLoading(prev => ({ ...prev, wards: true }));
    try {
      const options = await getAvailableBoundaryValues('wards', { 
        province: filters.province, 
        district: filters.district,
        municipality: filters.municipality 
      });
      setDropdownOptions(prev => ({ ...prev, wards: options }));
    } catch (err) {
      setError('Failed to load wards');
      console.error('Ward loading error:', err);
    } finally {
      setLoading(prev => ({ ...prev, wards: false }));
    }
  };

  const handleFilterChange = useCallback((level: keyof FilterState, value: string) => {
    setFilters(prev => {
      const newFilters = { ...prev };
      
      if (value) {
        newFilters[level] = value;
      } else {
        delete newFilters[level];
      }
      
      // Clear child filters when parent changes
      if (level === 'province') {
        delete newFilters.district;
        delete newFilters.municipality;
        delete newFilters.ward;
      } else if (level === 'district') {
        delete newFilters.municipality;
        delete newFilters.ward;
      } else if (level === 'municipality') {
        delete newFilters.ward;
      }
      
      return newFilters;
    });
  }, []);

  const handleClearFilters = useCallback(() => {
    setFilters({});
    setFilteredFeatures(null);
    onHighlightFeatures([]);
  }, [onHighlightFeatures]);

  const handleUseAsAOI = useCallback(() => {
    if (filteredFeatures?.features) {
      onRegionSelection(filteredFeatures.features);
    }
  }, [filteredFeatures, onRegionSelection]);

  const renderDropdown = (
    level: keyof DropdownState,
    label: string,
    value: string | undefined,
    options: FilterOption[],
    isLoading: boolean,
    disabled: boolean = false
  ) => (
    <Form.Group className="mb-3">
      <Form.Label className="small fw-bold">{label}</Form.Label>
      <div className="position-relative">
        <Form.Select
          size="sm"
          value={value || ''}
          onChange={(e) => handleFilterChange(level as keyof FilterState, e.target.value)}
          disabled={disabled || isLoading || options.length === 0}
        >
          <option value="">Select {label}</option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Form.Select>
        {isLoading && (
          <div className="position-absolute top-50 end-0 translate-middle-y me-3">
            <Spinner animation="border" size="sm" />
          </div>
        )}
      </div>
      {options.length === 0 && !isLoading && !disabled && (
        <small className="text-muted">No options available</small>
      )}
    </Form.Group>
  );

  if (!isVisible) return null;

  const hasActiveFilters = Object.values(filters).some(v => v);
  const hasResults = filteredFeatures?.features && filteredFeatures.features.length > 0;

  return (
    <Card className="interactive-boundary-filter">
      <Card.Header className="bg-info text-white py-2">
        <div className="d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center">
            <Filter size={16} className="me-2" />
            <span className="small fw-bold">Interactive Boundary Filter</span>
          </div>
          {hasActiveFilters && (
            <Button
              variant="outline-light"
              size="sm"
              onClick={handleClearFilters}
              title="Clear all filters"
            >
              <X size={14} />
            </Button>
          )}
        </div>
      </Card.Header>

      <Card.Body className="p-3">
        {error && (
          <Alert variant="danger" className="py-2 small mb-3">
            {error}
          </Alert>
        )}

        <div className="filter-cascade">
          {renderDropdown(
            'provinces',
            'Province',
            filters.province,
            dropdownOptions.provinces,
            loading.provinces
          )}

          {renderDropdown(
            'districts',
            'District',
            filters.district,
            dropdownOptions.districts,
            loading.districts,
            !filters.province
          )}

          {renderDropdown(
            'municipalities',
            'Municipality',
            filters.municipality,
            dropdownOptions.municipalities,
            loading.municipalities,
            !filters.district
          )}

          {renderDropdown(
            'wards',
            'Ward',
            filters.ward,
            dropdownOptions.wards,
            loading.wards,
            !filters.municipality
          )}
        </div>

        {/* Results Summary */}
        {hasActiveFilters && (
          <div className="results-section mt-3 pt-3 border-top">
            {loading.features ? (
              <div className="text-center">
                <Spinner animation="border" size="sm" className="me-2" />
                <span className="small">Loading boundaries...</span>
              </div>
            ) : hasResults ? (
              <div>
                <div className="d-flex align-items-center justify-content-between mb-2">
                  <Badge bg="success" className="d-flex align-items-center">
                    <Eye size={12} className="me-1" />
                    {filteredFeatures?.totalCount} boundaries found
                    {filteredFeatures?.isPartial && ' (partial)'}
                  </Badge>
                  {filteredFeatures?.isPartial && (
                    <small className="text-warning">
                      Results limited for performance
                    </small>
                  )}
                </div>

                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleUseAsAOI}
                  className="w-100 d-flex align-items-center justify-content-center"
                >
                  <MapPin size={12} className="me-1" />
                  Use as Area of Interest
                </Button>
              </div>
            ) : (
              <Alert variant="info" className="py-2 small mb-0">
                No boundaries found for current selection
              </Alert>
            )}
          </div>
        )}

        {!hasActiveFilters && (
          <div className="text-center py-3">
            <small className="text-muted">
              Select administrative boundaries to filter and highlight areas on the map
            </small>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default InteractiveBoundaryFilter;
