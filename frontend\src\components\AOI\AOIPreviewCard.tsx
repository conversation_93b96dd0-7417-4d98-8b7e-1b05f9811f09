import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>ge, <PERSON>, <PERSON><PERSON>, <PERSON>, Col } from 'react-bootstrap';
import { MapPin, Download, Eye, RefreshCw, Info } from 'lucide-react';
import { generateAOIScreenshot } from '../../services/geoserverService';

interface AOIPreviewCardProps {
  aoiData: {
    type: 'administrative' | 'drawn' | 'pin';
    level?: 'province' | 'district' | 'municipality' | 'ward';
    name?: string;
    code?: string;
    bounds: {
      north: number;
      south: number;
      east: number;
      west: number;
    };
    area: number; // in square kilometers
    coordinates?: any; // For drawn polygons or pin coordinates
    shape?: 'square' | 'circle'; // For pin areas
    dateRange?: {
      startDate: string;
      endDate: string;
    };
  };
  selectedLayers: string[];
  selectedBasemap?: string;
  onDownload: (selectedLayers: string[], aoiData: any) => void;
  onShowDetails?: () => void;
}

const AOIPreviewCard: React.FC<AOIPreviewCardProps> = ({
  aoiData,
  selectedLayers,
  selectedBasemap = 'osm:osm',
  onDownload,
  onShowDetails
}) => {
  const [screenshotUrl, setScreenshotUrl] = useState<string | null>(null);
  const [isGeneratingScreenshot, setIsGeneratingScreenshot] = useState(false);
  const [screenshotError, setScreenshotError] = useState<string | null>(null);

  // Generate screenshot when component mounts or data changes
  useEffect(() => {
    generateScreenshot();
    
    // Cleanup blob URL when component unmounts
    return () => {
      if (screenshotUrl) {
        URL.revokeObjectURL(screenshotUrl);
      }
    };
  }, [aoiData, selectedLayers, selectedBasemap]);

  const generateScreenshot = async () => {
    setIsGeneratingScreenshot(true);
    setScreenshotError(null);

    // Cleanup previous screenshot URL
    if (screenshotUrl) {
      URL.revokeObjectURL(screenshotUrl);
      setScreenshotUrl(null);
    }

    try {
      console.log('Generating AOI preview for:', aoiData);
      
      const url = await generateAOIScreenshot({
        bounds: aoiData.bounds,
        selectedLayers: selectedLayers.length > 0 ? selectedLayers : [], // Include base map even if no layers
        selectedBasemap,
        dimensions: { width: 400, height: 200 },
        format: 'png'
      });

      setScreenshotUrl(url);
    } catch (error) {
      console.error('Failed to generate AOI screenshot:', error);
      setScreenshotError(error instanceof Error ? error.message : 'Failed to generate preview');
    } finally {
      setIsGeneratingScreenshot(false);
    }
  };

  const handleDownload = () => {
    onDownload(selectedLayers, aoiData);
  };

  const formatArea = (area: number) => {
    if (area < 1) {
      return `${(area * 100).toFixed(0)} hectares`;
    } else if (area < 1000) {
      return `${area.toFixed(1)} km²`;
    } else {
      return `${(area / 1000).toFixed(1)}k km²`;
    }
  };

  const getLevelIcon = (level?: string) => {
    if (aoiData.type === 'pin') return '📌'; // For pin areas
    if (!level) return '✏️'; // For drawn polygons
    switch (level) {
      case 'province': return '🏛️';
      case 'district': return '🏢';
      case 'municipality': return '🏘️';
      case 'ward': return '🏠';
      default: return '📍';
    }
  };

  const getLevelColor = (level?: string) => {
    if (aoiData.type === 'pin') return 'warning'; // For pin areas
    if (!level) return 'success'; // For drawn polygons
    switch (level) {
      case 'province': return 'primary';
      case 'district': return 'success';
      case 'municipality': return 'warning';
      case 'ward': return 'info';
      default: return 'secondary';
    }
  };

  return (
    <Card className="mb-3" style={{ border: '2px solid #28a745', backgroundColor: '#f8fff9' }}>
      <Card.Header 
        style={{ 
          backgroundColor: '#28a745', 
          color: 'white',
          padding: '0.75rem 1rem'
        }}
      >
        <div className="d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center">
            <span className="me-2">{getLevelIcon(aoiData.level)}</span>
            <h6 className="mb-0">Selected Area of Interest</h6>
          </div>
          <Badge bg="light" text="dark" style={{ fontSize: '0.7rem' }}>
            {aoiData.type === 'drawn' ? 'Drawn Area' : 
             aoiData.type === 'pin' ? `Pin Area (${aoiData.shape || 'square'})` :
             aoiData.level ? aoiData.level.charAt(0).toUpperCase() + aoiData.level.slice(1) : 'Custom Area'}
          </Badge>
        </div>
      </Card.Header>
      
      <Card.Body style={{ padding: '1rem' }}>
        <Row>
          <Col md={7}>
            {/* Area Info */}
            <div className="mb-3">
              <h6 className="text-success mb-2 d-flex align-items-center">
                <MapPin size={16} className="me-2" />
                {aoiData.type === 'drawn' ? 'Custom Drawn Area' : 
                 aoiData.type === 'pin' ? (aoiData.name || 'Pin Area of Interest') :
                 aoiData.name || 'Area of Interest'}
              </h6>
              <div className="d-flex gap-2 flex-wrap">
                <Badge bg={getLevelColor(aoiData.level)} style={{ fontSize: '0.75rem' }}>
                  {formatArea(aoiData.area)}
                </Badge>
                {aoiData.code && (
                  <Badge bg="secondary" style={{ fontSize: '0.75rem' }}>
                    {aoiData.code}
                  </Badge>
                )}
                <Badge bg="info" style={{ fontSize: '0.75rem' }}>
                  {selectedLayers.length} layers
                </Badge>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="d-flex gap-2 flex-wrap">
              <Button 
                variant="success" 
                size="sm"
                onClick={handleDownload}
                disabled={selectedLayers.length === 0}
                className="d-flex align-items-center"
              >
                <Download size={14} className="me-1" />
                Download Data
              </Button>
              
              {onShowDetails && (
                <Button 
                  variant="outline-info" 
                  size="sm"
                  onClick={onShowDetails}
                  className="d-flex align-items-center"
                >
                  <Info size={14} className="me-1" />
                  Details
                </Button>
              )}
            </div>

            {selectedLayers.length === 0 && (
              <Alert variant="warning" className="mt-3 mb-0" style={{ fontSize: '0.85rem' }}>
                💡 Select layers from the Data Layers section below to enable download
              </Alert>
            )}
          </Col>
          
          <Col md={5}>
            {/* Preview Image */}
            <div className="text-center">
              <div 
                style={{
                  width: '100%',
                  height: '120px',
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #dee2e6',
                  borderRadius: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                {isGeneratingScreenshot ? (
                  <div className="text-center">
                    <Spinner animation="border" size="sm" className="mb-2" />
                    <div style={{ fontSize: '0.8rem', color: '#6c757d' }}>
                      Generating preview...
                    </div>
                  </div>
                ) : screenshotError ? (
                  <div className="text-center p-2">
                    <div style={{ fontSize: '0.8rem', color: '#dc3545', marginBottom: '8px' }}>
                      Preview unavailable
                    </div>
                    <Button 
                      variant="outline-secondary" 
                      size="sm"
                      onClick={generateScreenshot}
                      className="d-flex align-items-center mx-auto"
                    >
                      <RefreshCw size={12} className="me-1" />
                      Retry
                    </Button>
                  </div>
                ) : screenshotUrl ? (
                  <img
                    src={screenshotUrl}
                    alt={`Preview of ${aoiData.name}`}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      borderRadius: '4px'
                    }}
                  />
                ) : (
                  <div className="text-center">
                    <Eye size={20} className="text-muted mb-1" />
                    <div style={{ fontSize: '0.8rem', color: '#6c757d' }}>
                      Preview
                    </div>
                  </div>
                )}
              </div>
              
              <Button
                variant="link"
                size="sm"
                onClick={generateScreenshot}
                className="mt-1 p-0"
                style={{ fontSize: '0.75rem' }}
              >
                <RefreshCw size={10} className="me-1" />
                Refresh
              </Button>
            </div>
          </Col>
        </Row>
      </Card.Body>
    </Card>
  );
};

export default AOIPreviewCard;
