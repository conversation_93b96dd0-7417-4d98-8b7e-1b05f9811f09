/**
 * Server-side secure request utility that bypasses SSL certificate validation
 * 
 * IMPORTANT: This is for development purposes only and should not be used in production
 * without proper certificate validation.
 */
const axios = require('axios');
const https = require('https');

// Create a custom HTTPS agent that ignores certificate validation errors
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

// Create a secure axios instance with certificate validation bypassed
const secureAxios = axios.create({
  httpsAgent,
  timeout: 30000
});

// Add response interceptor for consistent error handling
secureAxios.interceptors.response.use(
  response => response,
  error => {
    console.error('[Secure Request Error]', error.message);
    return Promise.reject(error);
  }
);

/**
 * Make a secure GET request with certificate validation bypassed
 * 
 * @param {string} url - The URL to request
 * @param {object} options - Request options (headers, params, etc.)
 * @returns {Promise} - Axios response promise
 */
const secureGet = async (url, options = {}) => {
  console.log(`🔒 Secure GET request to: ${url}`);
  console.log(`🔒 Options:`, JSON.stringify(options, null, 2));
  try {
    const response = await secureAxios.get(url, options);
    console.log(`🔒 Response received: ${response.status}`);
    return response;
  } catch (error) {
    console.error(`[Secure GET Error] ${url}: ${error.message}`);
    throw error;
  }
};

/**
 * Make a secure POST request with certificate validation bypassed
 * 
 * @param {string} url - The URL to request
 * @param {object} data - POST data
 * @param {object} options - Request options (headers, etc.)
 * @returns {Promise} - Axios response promise
 */
const securePost = async (url, data = {}, options = {}) => {
  try {
    return await secureAxios.post(url, data, options);
  } catch (error) {
    console.error(`[Secure POST Error] ${url}: ${error.message}`);
    throw error;
  }
};

module.exports = {
  secureAxios,
  secureGet,
  securePost
};
