import { useState, useEffect, useCallback, useMemo } from 'react';
import { Form } from 'react-bootstrap';
import RegionSelector from './RegionSelector';
import DataLayers from './DataLayers';
import DataActions from './DataActions';
import AOIPreviewCard from '../AOI/AOIPreviewCard';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import './Sidebar.css';
import { LayerDiscovery } from '../../types/discovery';
import {
  loadProvinces,
  loadMunicipalities,
  loadDistricts,
  loadWards,
  getFilteredBoundaryFeatures,
  getSelectedBoundaryGeometry,
  AdministrativeRegion,
  BoundaryFilters
} from '../../services/unifiedBoundaryService';

interface SidebarProps {
  layers: LayerDiscovery[];
  selectedLayerNames: string[];
  onLayerChange: (layerName: string) => void;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  onDateChange: (type: 'startDate' | 'endDate', value: string) => void;
  onSearch: (query: string) => void;
  onPreviewData: () => void;
  onDownloadData: () => void;
  onQueryTemporalData?: () => void;
  isLoading?: boolean;
  error?: string | null;
  // AOI functionality
  onDrawModeToggle: (isDrawing: boolean) => void;
  isDrawingMode: boolean;
  hasDrawnArea: boolean;
  onClearDrawnArea: () => void;
  // Regional AOI functionality
  aoiMethod: 'drawn' | 'regional';
  onAOIMethodChange: (method: 'drawn' | 'regional') => void;
  hasRegionalSelection: boolean;
  onConfigureRegions: () => void;
  onClearRegionalSelection: () => void;
  // Predefined polygon functionality
  onPredefinedPolygon: (size: string) => void;
  // Basemap functionality
  selectedBasemap?: string;
  onBasemapChange?: (basemapName: string) => void;
  // Coordinate functionality
  onCoordinatePinModeToggle?: (enabled: boolean) => void;
  currentCoordinates?: string;
  // AOI Preview functionality
  aoiPreviewData?: any;
  onAOIDownload?: (selectedLayers: string[], aoiData: any) => void;
  onAOIPreview?: (aoiData: any) => void;
  // Interactive boundary filtering
  onBoundaryHighlight?: (features: GeoJSON.Feature[]) => void;
  onBoundaryRegionSelection?: (features: GeoJSON.Feature[]) => void;
}

function Sidebar(props: SidebarProps) {
  // State for sidebar collapse
  const [isCollapsed, setIsCollapsed] = useState(false);

  // State for region of interest toggle
  const [selectRegionOfInterest, setSelectRegionOfInterest] = useState(false);

  // State for nested section toggles
  const [nestedSections, setNestedSections] = useState({
    administrative: true,  // Expanded by default
    coordinates: false,    // Collapsed by default
    dateRange: false,      // Collapsed by default
    drawingTools: false    // Collapsed by default
  });

  // State for coordinate input
  const [isPinningMode, setIsPinningMode] = useState(false);

  // Local AOI preview state for sidebar display
  const [localAoiPreviewData, setLocalAoiPreviewData] = useState<any>(null);

  // State for administrative boundaries - updated to use unified types
  const [administrativeBoundaries, setAdministrativeBoundaries] = useState({
    provinces: [] as AdministrativeRegion[],
    municipalities: [] as AdministrativeRegion[],
    districts: [] as AdministrativeRegion[],
    wards: [] as AdministrativeRegion[]
  });

  // State for selected administrative regions (with codes)
  const [selectedRegions, setSelectedRegions] = useState({
    province: '', // province id
    provinceName: '', // province name
    municipality: '',
    municipalityCode: '',
    district: '',
    districtCode: '',
    ward: '',
    wardCode: ''
  });

  // Loading states for administrative boundaries
  const [boundaryLoading, setBoundaryLoading] = useState({
    provinces: false,
    municipalities: false,
    districts: false,
    wards: false
  });

  const toggleNestedSection = (section: keyof typeof nestedSections) => {
    const newState = !nestedSections[section];
    
    setNestedSections(prev => ({
      ...prev,
      [section]: newState
    }));
    
    // If toggling coordinates section, handle pin mode
    if (section === 'coordinates') {
      // When closing coordinates section, also disable pin mode
      if (!newState && isPinningMode) {
        setIsPinningMode(false);
        if (props.onCoordinatePinModeToggle) {
          props.onCoordinatePinModeToggle(false);
        }
      }
    }
  };

  // Use props directly instead of destructuring

  // Handle external AOI preview data (from drawn polygons)
  useEffect(() => {
    if (props.aoiPreviewData) {
      // Clear local AOI preview when external data is provided
      setLocalAoiPreviewData(null);
    }
  }, [props.aoiPreviewData]);

  // Clear all AOI states when "Select region of interest" toggle is turned off
  useEffect(() => {
    if (!selectRegionOfInterest) {
      console.log('🧹 Clearing all AOI states - region of interest toggle disabled');
      
      // Clear selected regions
      setSelectedRegions({
        province: '',
        provinceName: '',
        municipality: '',
        municipalityCode: '',
        district: '',
        districtCode: '',
        ward: '',
        wardCode: ''
      });
      
      // Clear AOI preview data
      setLocalAoiPreviewData(null);
      
      // Clear highlighted boundaries if prop is available
      if (props.onBoundaryHighlight) {
        props.onBoundaryHighlight([]);
      }
    }
  }, [selectRegionOfInterest, props.onBoundaryHighlight]);

  // Clear administrative boundaries when pin mode is active
  useEffect(() => {
    if (isPinningMode) {
      // Clear all administrative selections when entering pin mode
      setSelectedRegions({
        province: '',
        provinceName: '',
        municipality: '',
        municipalityCode: '',
        district: '',
        districtCode: '',
        ward: '',
        wardCode: ''
      });
      
      // Clear local AOI preview
      setLocalAoiPreviewData(null);
      
      console.log('Pin mode activated - cleared administrative boundaries');
    }
  }, [isPinningMode]);

  // Load provinces on component mount
  useEffect(() => {
    const loadProvincesData = async () => {
      setBoundaryLoading(prev => ({ ...prev, provinces: true }));
      try {
        const provinces = await loadProvinces();
        setAdministrativeBoundaries(prev => ({ ...prev, provinces }));
      } catch (error) {
        console.error('Failed to load provinces:', error);
      } finally {
        setBoundaryLoading(prev => ({ ...prev, provinces: false }));
      }
    };

    loadProvincesData();
  }, []);

  // Load municipalities when province changes
  useEffect(() => {
    if (selectedRegions.province) {
      const loadMunicipalitiesData = async () => {
        setBoundaryLoading(prev => ({ ...prev, municipalities: true }));
        try {
          const municipalities = await loadMunicipalities(selectedRegions.province);
          setAdministrativeBoundaries(prev => ({ ...prev, municipalities }));
          // Clear dependent selections
          setSelectedRegions(prev => ({
            ...prev,
            municipality: '',
            municipalityCode: '',
            district: '',
            districtCode: '',
            ward: '',
            wardCode: ''
          }));
        } catch (error) {
          console.error('Failed to load municipalities:', error);
        } finally {
          setBoundaryLoading(prev => ({ ...prev, municipalities: false }));
        }
      };

      loadMunicipalitiesData();
    } else {
      // Clear municipalities if no province selected
      setAdministrativeBoundaries(prev => ({ ...prev, municipalities: [], districts: [], wards: [] }));
      setSelectedRegions(prev => ({
        ...prev,
        municipality: '',
        municipalityCode: '',
        district: '',
        districtCode: '',
        ward: '',
        wardCode: ''
      }));
    }
  }, [selectedRegions.province]);

  // Load districts when province changes (if available)
  useEffect(() => {
    if (selectedRegions.province) {
      const loadDistrictsData = async () => {
        setBoundaryLoading(prev => ({ ...prev, districts: true }));
        try {
          const districts = await loadDistricts(selectedRegions.province);
          setAdministrativeBoundaries(prev => ({ ...prev, districts }));
        } catch (error) {
          console.error('Failed to load districts:', error);
        } finally {
          setBoundaryLoading(prev => ({ ...prev, districts: false }));
        }
      };

      loadDistrictsData();
    }
  }, [selectedRegions.province]);
  
  // Load municipalities for district (handles metros and non-metros)
  useEffect(() => {
    const selectedDistrictInfo = administrativeBoundaries.districts.find(
      (d: AdministrativeRegion) => d.name === selectedRegions.district
    );
    
    if (selectedRegions.district && selectedRegions.province) {
      const loadMunicipalitiesForDistrict = async () => {
        setBoundaryLoading(prev => ({ ...prev, municipalities: true }));
        try {
          if (selectedDistrictInfo?.properties?.isMetro) {
            // Metro areas skip to wards - no local municipalities
            console.log(`District ${selectedRegions.district} is a metropolitan municipality - skipping to wards`);
            
            // For metros, we need to set up the necessary data to load wards directly
            setAdministrativeBoundaries(prev => ({ ...prev, municipalities: [] }));
            
            // Even though the UI won't show municipalities for metros,
            // set the municipality code to the district code to help with ward loading
            setSelectedRegions(prev => ({
              ...prev,
              municipality: selectedRegions.district, // Use district name as municipality name for metros
              municipalityCode: selectedDistrictInfo.properties?.code || selectedDistrictInfo.id, // Use district code as municipality code for metros
              ward: '',
              wardCode: ''
            }));
          } else {
            // Load local municipalities for this district

            const municipalities = await loadMunicipalities(selectedRegions.province, selectedRegions.district);

            
            setAdministrativeBoundaries(prev => ({ ...prev, municipalities }));
            // Clear dependent selections
            setSelectedRegions(prev => ({
              ...prev,
              municipality: '',
              municipalityCode: '',
              ward: '',
              wardCode: ''
            }));
          }
        } catch (error) {
          console.error('Failed to load municipalities for district:', error);
        } finally {
          setBoundaryLoading(prev => ({ ...prev, municipalities: false }));
        }
      };

      loadMunicipalitiesForDistrict();
    }
  }, [selectedRegions.district, selectedRegions.province, administrativeBoundaries.districts]);

  // Load wards when municipality changes OR when district changes (for metros)
  useEffect(() => {
    // Check if we should load wards for a municipality or for a metro district
    const isMetroDistrict = selectedRegions.district && administrativeBoundaries.districts.find(
      d => d.name === selectedRegions.district && d.properties?.isMetro === true
    );
    
    const shouldLoadWards = selectedRegions.municipalityCode || isMetroDistrict;
    

    if (shouldLoadWards) {
      const loadWardsData = async () => {
        setBoundaryLoading(prev => ({ ...prev, wards: true }));
        try {
          let wardParentCode = '';
          
          if (isMetroDistrict) {
            // For metro districts, use the district code directly
            const districtInfo = administrativeBoundaries.districts.find(
              d => d.name === selectedRegions.district
            );
            wardParentCode = districtInfo?.id || selectedRegions.districtCode;
            console.log('Loading wards for metro district:', selectedRegions.district, 'with code:', wardParentCode);
          } else {
            // For regular municipalities, use the municipality code
            wardParentCode = selectedRegions.municipalityCode;
            console.log('Loading wards for municipality:', selectedRegions.municipality, 'with code:', wardParentCode);
          }
          
          if (wardParentCode) {
            const wards = await loadWards(wardParentCode);
            console.log("Loaded wards:", wards);
            setAdministrativeBoundaries(prev => ({ ...prev, wards }));
          } else {
            console.warn('No parent code available for ward loading');
            setAdministrativeBoundaries(prev => ({ ...prev, wards: [] }));
          }
          
          // Clear dependent selections
          setSelectedRegions(prev => ({ ...prev, ward: '', wardCode: '' }));
        } catch (error) {
          console.error('Failed to load wards:', error);
          setAdministrativeBoundaries(prev => ({ ...prev, wards: [] }));
        } finally {
          setBoundaryLoading(prev => ({ ...prev, wards: false }));
        }
      };

      loadWardsData();
    } else {
      // Clear wards if no municipality or metro district selected
      setAdministrativeBoundaries(prev => ({ ...prev, wards: [] }));
      setSelectedRegions(prev => ({ ...prev, ward: '', wardCode: '' }));
    }
  }, [selectedRegions.municipality, selectedRegions.municipalityCode, selectedRegions.district, selectedRegions.districtCode, selectedRegions.province, administrativeBoundaries.districts]);

  // Handler for administrative region selection with auto-trigger functionality
  const handleRegionChange = (level: keyof typeof selectedRegions, value: string) => {
    console.log(`Changing ${level} to:`, value);
    const updates: any = { [level]: value };

    if (level === 'province') {
      // Find province name by id
      const selectedProvince = administrativeBoundaries.provinces.find(p => p.id === value);
      updates.provinceName = selectedProvince ? selectedProvince.name : '';
      // Clear all dependent selections
      updates.district = '';
      updates.districtCode = '';
      updates.municipality = '';
      updates.municipalityCode = '';
      updates.ward = '';
      updates.wardCode = '';
    }

    // When district changes, also store the district code
    if (level === 'district') {
      const selectedDistrictInfo = administrativeBoundaries.districts.find(
        d => d.name === value
      );
      updates.districtCode = selectedDistrictInfo?.id || '';
      console.log("Selected district info:", selectedDistrictInfo);
      
      // Clear dependent selections
      updates.municipality = '';
      updates.municipalityCode = '';
      updates.ward = '';
      updates.wardCode = '';
    }

    // When municipality changes, also store the municipality code
    if (level === 'municipality') {
      // The dropdown is now using the municipality name as value
      updates.municipality = value;
      
      // Find and store the code for API calls
      const selectedMunicipalityInfo = administrativeBoundaries.municipalities.find(
        m => m.name === value
      );
      updates.municipalityCode = selectedMunicipalityInfo?.id || '';
      console.log("Selected municipality:", selectedMunicipalityInfo);
      
      // Clear dependent selections
      updates.ward = '';
      updates.wardCode = '';
    }

    // When ward changes, also store the ward code
    if (level === 'ward') {
      updates.ward = value;
      
      const selectedWardInfo = administrativeBoundaries.wards.find(
        w => w.name === value
      );
      updates.wardCode = selectedWardInfo?.id || '';
      console.log("Selected ward:", selectedWardInfo);
    }

    setSelectedRegions(prev => ({
      ...prev,
      ...updates
    }));
  };

  // Auto-trigger map rendering and AOI preview when regions change
  useEffect(() => {
    const triggerBoundaryUpdates = async () => {
      // Only trigger if we have at least a province selected
      if (!selectedRegions.provinceName) {
        // Clear map rendering and AOI preview
        if (props.onBoundaryHighlight) {
          props.onBoundaryHighlight([]);
        }
        setLocalAoiPreviewData(null);
        return;
      }

      try {
        // Build filters based on current selections
        const filters: BoundaryFilters = {};
        
        if (selectedRegions.provinceName) {
          filters.province = selectedRegions.provinceName;
        }
        if (selectedRegions.district) {
          filters.district = selectedRegions.district;
        }
        if (selectedRegions.municipality) {
          filters.municipality = selectedRegions.municipality;
        }
        if (selectedRegions.ward) {
          filters.ward = selectedRegions.ward;
        }

        console.log('🎯 Triggering boundary updates with filters:', filters);

        // Get filtered boundary features for map rendering
        const boundaryResult = await getFilteredBoundaryFeatures(filters);
        
        if (boundaryResult.features.length > 0) {
          // Always trigger map highlighting (this shows boundaries during selection)
          if (props.onBoundaryHighlight) {
            props.onBoundaryHighlight(boundaryResult.features);
          }

          // Only generate AOI preview data when the ENTIRE process is complete
          if (isLayerSelectionComplete()) {
            const selectedLevel = selectedRegions.ward ? 'ward' :
                                 selectedRegions.municipality ? 'municipality' :
                                 selectedRegions.district ? 'district' : 'province';
            
            const selectedName = selectedRegions.ward || 
                                selectedRegions.municipality || 
                                selectedRegions.district || 
                                selectedRegions.provinceName;

            // Fetch precise geometry for the selected boundary
            const fetchAOIGeometry = async () => {
              try {
                console.log('🎯 Fetching precise geometry for AOI clipping...');

                const filters = {
                  province: selectedRegions.provinceName,
                  district: selectedRegions.district,
                  municipality: selectedRegions.municipality,
                  ward: selectedRegions.ward
                };

                console.log('🔍 Geometry filters:', filters);

                const geometryResult = await getSelectedBoundaryGeometry(filters);

                console.log('🔍 Geometry result:', {
                  hasGeometry: !!geometryResult.geometry,
                  hasFeature: !!geometryResult.feature,
                  hasBounds: !!geometryResult.bounds,
                  geometryType: geometryResult.geometry?.type,
                  coordinatesLength: (geometryResult.geometry as any)?.coordinates?.length
                });

                if (!geometryResult.geometry) {
                  console.warn('⚠️ No geometry retrieved - AOI clipping will use bounds only');
                } else {
                  console.log('✅ Retrieved geometry for boundary:', geometryResult.geometry.type);
                }
                
                const aoiData = {
                  type: 'administrative' as const,
                  level: selectedLevel,
                  name: selectedName,
                  code: selectedRegions.wardCode || 
                        selectedRegions.municipalityCode || 
                        selectedRegions.districtCode || 
                        selectedRegions.province,
                  bounds: geometryResult.bounds || boundaryResult.bounds || {
                    north: -22.0,
                    south: -35.0,
                    east: 33.0,
                    west: 16.0
                  },
                  area: boundaryResult.area || 1000,
                  // Add precise geometry for clipping
                  geometry: geometryResult.geometry,
                  feature: geometryResult.feature
                };

                console.log('🗺️ Generated AOI preview data with geometry:', aoiData);
                setLocalAoiPreviewData(aoiData);
              } catch (error) {
                console.error('Failed to fetch AOI geometry:', error);
                
                // Fallback to bounds-only AOI data
                const aoiData = {
                  type: 'administrative' as const,
                  level: selectedLevel,
                  name: selectedName,
                  code: selectedRegions.wardCode || 
                        selectedRegions.municipalityCode || 
                        selectedRegions.districtCode || 
                        selectedRegions.province,
                  bounds: boundaryResult.bounds || {
                    north: -22.0,
                    south: -35.0,
                    east: 33.0,
                    west: 16.0
                  },
                  area: boundaryResult.area || 1000
                };
                
                setLocalAoiPreviewData(aoiData);
              }
            };
            
            fetchAOIGeometry();
          } else {
            // Clear AOI preview if process is not complete
            setLocalAoiPreviewData(null);
          }

          // Trigger region selection callback for additional handling
          if (props.onBoundaryRegionSelection) {
            props.onBoundaryRegionSelection(boundaryResult.features);
          }
        } else {
          console.log('No boundary features found for current selection');
          if (props.onBoundaryHighlight) {
            props.onBoundaryHighlight([]);
          }
          setLocalAoiPreviewData(null);
        }
      } catch (error) {
        console.error('Failed to trigger boundary updates:', error);
        if (props.onBoundaryHighlight) {
          props.onBoundaryHighlight([]);
        }
        setLocalAoiPreviewData(null);
      }
    };

    // Debounce the updates to avoid excessive API calls
    const debounceTimer = setTimeout(triggerBoundaryUpdates, 300);
    return () => clearTimeout(debounceTimer);
  }, [
    selectedRegions.provinceName,
    selectedRegions.district,
    selectedRegions.municipality,
    selectedRegions.ward
  ]); // Removed props.onBoundaryHighlight and props.onBoundaryRegionSelection to prevent continuous re-renders

  // Helper functions to determine completion states for sequential workflow - memoized to prevent re-renders
  const isAdministrativeSelectionComplete = useCallback(() => {
    // Must have at least province
    if (!selectedRegions.provinceName) return false;
    
    // If no district selected, just province is enough for completion
    if (!selectedRegions.district) return true;
    
    // Check if district is metro
    const isMetro = administrativeBoundaries.districts.find(d => 
      d.name === selectedRegions.district && d.properties?.isMetro
    );
    
    if (isMetro) {
      // For metro districts, if no wards available, district selection is complete
      if (administrativeBoundaries.wards.length === 0 && !boundaryLoading.wards) return true;
      // If wards available, need ward selection for completion
      return !!selectedRegions.ward;
    }
    
    // For non-metro districts, check municipality selection
    if (selectedRegions.municipality) {
      // If no wards available for municipality, municipality selection is complete
      if (administrativeBoundaries.wards.length === 0 && !boundaryLoading.wards) return true;
      // If wards available, need ward selection for completion
      return !!selectedRegions.ward;
    }
    
    // If no municipalities available for district, district selection is complete
    if (administrativeBoundaries.municipalities.length === 0 && !boundaryLoading.municipalities) return true;
    
    // Otherwise, need further selection
    return false;
  }, [
    selectedRegions.provinceName, 
    selectedRegions.district, 
    selectedRegions.municipality, 
    selectedRegions.ward,
    administrativeBoundaries.districts,
    administrativeBoundaries.municipalities,
    administrativeBoundaries.wards,
    boundaryLoading.wards,
    boundaryLoading.municipalities
  ]);

  const isDateRangeComplete = useCallback(() => {
    return isAdministrativeSelectionComplete() && 
           props.dateRange.startDate && 
           props.dateRange.endDate;
  }, [isAdministrativeSelectionComplete, props.dateRange.startDate, props.dateRange.endDate]);

  const isLayerSelectionComplete = useCallback(() => {
    return isDateRangeComplete() && props.selectedLayerNames.length > 0;
  }, [isDateRangeComplete, props.selectedLayerNames.length]);

  // Auto-expand date range section when administrative selection is complete
  useEffect(() => {
    if (isAdministrativeSelectionComplete() && !nestedSections.dateRange) {
      setNestedSections(prev => ({ ...prev, dateRange: true }));
    }
  }, [isAdministrativeSelectionComplete, nestedSections.dateRange]);

  // Separate useEffect to update AOI preview when layer selection becomes complete
  useEffect(() => {
    // Only update AOI preview when we transition to layer selection complete
    // and we have some boundary data available
    if (isLayerSelectionComplete() && !localAoiPreviewData) {
      // Check if we have sufficient data to generate AOI preview
      if (selectedRegions.provinceName) {
        const selectedLevel = selectedRegions.ward ? 'ward' :
                             selectedRegions.municipality ? 'municipality' :
                             selectedRegions.district ? 'district' : 'province';
        
        const selectedName = selectedRegions.ward || 
                            selectedRegions.municipality || 
                            selectedRegions.district || 
                            selectedRegions.provinceName;

        const aoiData = {
          type: 'administrative' as const,
          level: selectedLevel,
          name: selectedName,
          code: selectedRegions.wardCode || 
                selectedRegions.municipalityCode || 
                selectedRegions.districtCode || 
                selectedRegions.province,
          bounds: {
            north: -22.0,
            south: -35.0,
            east: 33.0,
            west: 16.0
          },
          area: 1000
        };

        console.log('🎯 Layer selection complete - generating AOI preview:', aoiData);
        setLocalAoiPreviewData(aoiData);
      }
    } else if (!isLayerSelectionComplete() && localAoiPreviewData) {
      // Clear AOI preview if layer selection is no longer complete
      setLocalAoiPreviewData(null);
    }
  }, [isLayerSelectionComplete, selectedRegions.provinceName, selectedRegions.district, selectedRegions.municipality, selectedRegions.ward, localAoiPreviewData]);

  // Load sidebar state from localStorage
  useEffect(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState) {
      setIsCollapsed(savedState === 'true');
    }
  }, []);

  // Save sidebar state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', isCollapsed.toString());
  }, [isCollapsed]);

  // Effect to update coordinates from props - handled by the input directly now
  // No local state needed since we use props.currentCoordinates directly

  // Effect to handle coordinate section state
  useEffect(() => {
    // If coordinates section is closed but pin mode is active, disable pin mode
    if (!nestedSections.coordinates && isPinningMode) {
      console.log('Auto-disabling pin mode because coordinates section is closed');
      setIsPinningMode(false);
      if (props.onCoordinatePinModeToggle) {
        props.onCoordinatePinModeToggle(false);
      }
    }
  }, [nestedSections.coordinates, isPinningMode]); // Removed props.onCoordinatePinModeToggle from dependencies

  // Toggle sidebar collapse state
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Let's also dispatch an event when the sidebar state changes
  useEffect(() => {
    window.dispatchEvent(
      new CustomEvent('sidebarToggle', {
        detail: { collapsed: isCollapsed }
      })
    );
  }, [isCollapsed]);

  // Check if any temporal layers are selected
  const hasTemporalLayers = props.selectedLayerNames.includes('soilMoisture');
  const getSelectedLayerName = () => (hasTemporalLayers ? 'Soil Moisture' : '');

  function onSearch(query: string): void {
    if (props.onSearch) {
      props.onSearch(query);
    }
  }

  function onDrawModeToggle(isDrawing: boolean): void {
    if (props.onDrawModeToggle) {
      props.onDrawModeToggle(isDrawing);
    }
  }

  function onClearDrawnArea(): void {
    if (props.onClearDrawnArea) {
      props.onClearDrawnArea();
    }
  }

  function onAOIMethodChange(method: 'drawn' | 'regional'): void {
    if (props.onAOIMethodChange) {
      props.onAOIMethodChange(method);
    }
  }

  function onConfigureRegions(): void {
    if (props.onConfigureRegions) {
      props.onConfigureRegions();
    }
  }

  // Generate AOI preview data for administrative boundaries
  const generateAOIPreview = (level: 'province' | 'district' | 'municipality' | 'ward', name: string, code?: string) => {
    // Generate rough bounds based on South African coordinates
    // In a real implementation, this would come from the WFS service
    const roughBounds = {
      north: -22.0,
      south: -35.0,
      east: 33.0,
      west: 16.0
    };
    
    // Rough area calculations (in km²) - in real implementation, this would be calculated from geometry
    const areaMap = {
      province: 120000, // ~120,000 km² average province size
      district: 25000,  // ~25,000 km² average district size  
      municipality: 8000, // ~8,000 km² average municipality size
      ward: 50          // ~50 km² average ward size
    };

    const aoiData = {
      type: 'administrative' as const,
      level,
      name,
      code,
      bounds: roughBounds,
      area: areaMap[level] || 1000
    };

    setLocalAoiPreviewData(aoiData);
  };

  const handleDownloadAOI = (selectedLayers: string[], aoiData: any) => {
    console.log('Downloading AOI data:', { selectedLayers, aoiData });
    // Call the parent's download handler if available
    if (props.onAOIDownload) {
      props.onAOIDownload(selectedLayers, aoiData);
    } else {
      // Fallback alert for now
      alert(`Download initiated!\n\nArea: ${aoiData.name}\nLayers: ${selectedLayers.length}\nLevel: ${aoiData.level}`);
    }
  };

  function onClearRegionalSelection(): void {
    // Clear all selected administrative regions and boundaries
    setSelectedRegions({
      province: '',
      provinceName: '',
      municipality: '',
      municipalityCode: '',
      district: '',
      districtCode: '',
      ward: '',
      wardCode: ''
    });
    setAdministrativeBoundaries(prev => ({
      ...prev,
      municipalities: [],
      districts: [],
      wards: []
    }));
  }

  function onPredefinedPolygon(size: string): void {
    if (props.onPredefinedPolygon) {
      props.onPredefinedPolygon(size);
    }
  }

  function onPreviewData(): void {
    throw new Error('Function not implemented.');
  }

  function onDownloadData(): void {
    throw new Error('Function not implemented.');
  }

  function onQueryTemporalData(): void {
    throw new Error('Function not implemented.');
  }

  return (
    <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="sidebar-header" style={{ display: 'flex', alignItems: 'center', justifyContent: isCollapsed ? 'center' : 'space-between', height: 56 }}>
        {!isCollapsed && (
          <h1 className="app-title" style={{ margin: 0, flex: 1 }}>DATA ACCESS</h1>
        )}
        <button
          className="sidebar-toggle"
          onClick={toggleSidebar}
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          style={isCollapsed ? { margin: 0 } : { marginLeft: 8 }}
        >
          {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
        </button>
      </div>
      {!isCollapsed && (
        <div className="sidebar-content">
          {/* Select Region of Interest Toggle */}
          <div className="sidebar-card">
            <div className="sidebar-card-body">
              <div className="d-flex align-items-center justify-content-between mb-3">
                <div className="d-flex align-items-center">
                  <span className="text-danger me-2">📍</span>
                  <span style={{ fontSize: '0.9rem', fontWeight: '500' }}>Select region of interest</span>
                </div>
                <Form.Check
                  type="switch"
                  id="region-toggle"
                  checked={selectRegionOfInterest}
                  onChange={(e) => setSelectRegionOfInterest(e.target.checked)}
                  className="custom-switch"
                />
              </div>
            </div>
          </div>

          {/* Region of Interest Card - Only show when toggle is ON */}
          {selectRegionOfInterest && (
            <div className="sidebar-card">
              <div className="sidebar-card-header">
                <h5 className="sidebar-card-title">Region of Interest</h5>
              </div>
              <div className="sidebar-card-body">
                {/* Administrative Boundaries Section */}
                <div className="nested-section mb-3">
                  <div
                    className="nested-section-header clickable"
                    onClick={() => toggleNestedSection('administrative')}
                  >
                    <div className="d-flex align-items-center">
                      <span className="nested-section-icon">🏛️</span>
                      <span className="nested-section-title">Administrative Boundaries</span>
                    </div>
                    <span className="nested-section-toggle">
                      {nestedSections.administrative ? '▼' : '▶'}
                    </span>
                  </div>
                  {nestedSections.administrative && (
                    <div className="nested-section-body">
                      {/* Help text */}
                      <div style={{ 
                        fontSize: '0.8rem', 
                        color: '#b3d9ff', 
                        marginBottom: '12px',
                        padding: '8px',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        borderRadius: '4px',
                        border: '1px solid rgba(0, 123, 255, 0.2)'
                      }}>
                        💡 Select your area of interest below. Boundaries will appear on the map as you make selections.
                      </div>
                      
                      <Form.Group className="mb-2">
                        <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Province:</Form.Label>
                        <Form.Select
                          size="sm"
                          style={{
                            fontSize: '0.85rem',
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            border: '1px solid rgba(255, 255, 255, 0.3)',
                            color: '#333'
                          }}
                          value={selectedRegions.province}
                          onChange={(e) => handleRegionChange('province', e.target.value)}
                          disabled={boundaryLoading.provinces}
                        >
                          <option value="">
                            {boundaryLoading.provinces ? '-- Loading Provinces --' : '-- Select Province --'}
                          </option>
                          {administrativeBoundaries.provinces.map((province) => (
                            <option key={province.id} value={province.id}>
                              {province.name}
                            </option>
                          ))}
                        </Form.Select>
                        {/* Loader for districts below province dropdown */}
                        {selectedRegions.province && boundaryLoading.districts && (
                          <div style={{ fontSize: '0.8rem', color: '#ffc107', marginTop: 4 }}>Loading districts...</div>
                        )}
                      </Form.Group>

                      {/* Show District dropdown only if province is selected and districts loaded */}
                      {selectedRegions.province && !boundaryLoading.districts && administrativeBoundaries.districts.length > 0 && (
                        <Form.Group className="mb-2">
                          <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>District:</Form.Label>
                          <Form.Select
                            size="sm"
                            style={{
                              fontSize: '0.85rem',
                              backgroundColor: 'rgba(255, 255, 255, 0.95)',
                              border: '1px solid rgba(255, 255, 255, 0.3)',
                              color: '#333'
                            }}
                            value={selectedRegions.district}
                            onChange={(e) => handleRegionChange('district', e.target.value)}
                          >
                            <option value="">-- Select District --</option>
                            {administrativeBoundaries.districts.map((district) => (
                              <option key={district.id} value={district.name}>
                                {district.name} {district.properties?.isMetro ? '🟦 Metro' : '🟩 District'}
                              </option>
                            ))}
                          </Form.Select>
                          {/* Loader for municipalities below district dropdown */}
                          {selectedRegions.district && boundaryLoading.municipalities && (
                            <div style={{ fontSize: '0.8rem', color: '#ffc107', marginTop: 4 }}>Loading municipalities...</div>
                          )}
                        </Form.Group>
                      )}

                      {/* Show Municipality dropdown if district is selected and municipalities loaded */}
                      {selectedRegions.district && !boundaryLoading.municipalities && (
                        administrativeBoundaries.municipalities.length > 0 ? (
                          <Form.Group className="mb-2">
                            <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Local Municipality:</Form.Label>
                            <Form.Select
                              size="sm"
                              style={{
                                fontSize: '0.85rem',
                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                border: '1px solid rgba(255, 255, 255, 0.3)',
                                color: '#333'
                              }}
                              value={selectedRegions.municipalityCode}
                              onChange={(e) => handleRegionChange('municipality', e.target.value)}
                            >
                              <option value="">-- Select Municipality --</option>
                              {administrativeBoundaries.municipalities.map((municipality) => (
                                <option key={municipality.id} value={municipality.code}>
                                  {municipality.name}
                                </option>
                              ))}
                            </Form.Select>
                            {/* Loader for wards below municipality dropdown */}
                            {selectedRegions.municipalityCode && boundaryLoading.wards && (
                              <div style={{ fontSize: '0.8rem', color: '#ffc107', marginTop: 4 }}>Loading wards...</div>
                            )}
                          </Form.Group>
                        ) : (
                          // No municipalities found, but we have a district
                          <div className="mb-2">
                            <div style={{ 
                              fontSize: '0.85rem', 
                              color: '#ffc107', 
                              marginBottom: 8,
                              padding: '6px 8px',
                              backgroundColor: 'rgba(255, 193, 7, 0.1)',
                              borderRadius: '4px',
                              border: '1px solid rgba(255, 193, 7, 0.3)'
                            }}>
                              ⚠️ No municipalities found for this district. Using district for map rendering and AOI.
                            </div>
                          </div>
                        )
                      )}

                      {/* Show Ward dropdown only if municipality is selected and wards loaded, or if metro skip municipality */}
                      {(selectedRegions.municipalityCode || 
                        (selectedRegions.district && administrativeBoundaries.districts.find(d => d.name === selectedRegions.district && d.properties?.isMetro))) && (
                        !boundaryLoading.wards && 
                        (administrativeBoundaries.wards.length > 0 ? (
                          <Form.Group className="mb-0">
                            <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Ward:</Form.Label>
                            <Form.Select
                              size="sm"
                              style={{
                                fontSize: '0.85rem',
                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                border: '1px solid rgba(255, 255, 255, 0.3)',
                                color: '#333'
                              }}
                              value={selectedRegions.wardCode}
                              onChange={(e) => handleRegionChange('ward', e.target.value)}
                            >
                              <option value="">-- Select Ward --</option>
                              {administrativeBoundaries.wards.map((ward) => (
                                <option key={ward.id} value={ward.id}>
                                  {ward.name}
                                </option>
                              ))}
                            </Form.Select>
                          </Form.Group>
                        ) : (
                          <div className="mb-0">
                            <div style={{ 
                              fontSize: '0.85rem', 
                              color: '#ffc107', 
                              marginBottom: 8,
                              padding: '6px 8px',
                              backgroundColor: 'rgba(255, 193, 7, 0.1)',
                              borderRadius: '4px',
                              border: '1px solid rgba(255, 193, 7, 0.3)'
                            }}>
                              ⚠️ No wards found for this selection
                            </div>
                            <button 
                              className="btn btn-sm btn-warning" 
                              style={{ 
                                fontSize: '0.85rem', 
                                width: '100%',
                                backgroundColor: '#ffc107',
                                borderColor: '#e0a800'
                              }}
                              onClick={() => {
                                // Use the current selection (municipality or district) as the AOI
                                if (selectedRegions.municipalityCode) {
                                  console.log("Using selected municipality as AOI:", selectedRegions.municipality);
                                  generateAOIPreview('municipality', selectedRegions.municipality, selectedRegions.municipalityCode);
                                  props.onConfigureRegions(); // This should update the map with the selected boundaries
                                } else if (selectedRegions.district) {
                                  console.log("Using selected district as AOI:", selectedRegions.district);
                                  generateAOIPreview('district', selectedRegions.district, selectedRegions.districtCode);
                                  props.onConfigureRegions();
                                }
                              }}
                            >
                              📍 Use Current {selectedRegions.municipalityCode ? 'Municipality' : 'District'} As Area of Interest
                            </button>
                          </div>
                        ))
                      )}
                    </div>
                  )}
                </div>

                {/* Date Range Section - Only show when administrative selection is complete */}
                {isAdministrativeSelectionComplete() && (
                  <div className="nested-section mb-3">
                    <div
                      className="nested-section-header clickable"
                      onClick={() => toggleNestedSection('dateRange')}
                    >
                      <div className="d-flex align-items-center">
                        <span className="nested-section-icon">📅</span>
                        <span className="nested-section-title">Date Range</span>
                        {!isDateRangeComplete() && (
                          <span style={{ color: '#ffc107', marginLeft: '8px', fontSize: '0.8rem' }}>
                            ⚠️ Required
                          </span>
                        )}
                      </div>
                      <span className="nested-section-toggle">
                        {nestedSections.dateRange ? '▼' : '▶'}
                      </span>
                    </div>
                    {nestedSections.dateRange && (
                      <div className="nested-section-body">
                        <div style={{ 
                          fontSize: '0.8rem', 
                          color: '#b3d9ff', 
                          marginBottom: '12px',
                          padding: '8px',
                          backgroundColor: 'rgba(0, 123, 255, 0.1)',
                          borderRadius: '4px',
                          border: '1px solid rgba(0, 123, 255, 0.2)'
                        }}>
                          📅 Select the time period for your analysis.
                        </div>
                        
                        <Form.Group className="mb-2">
                          <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Start Date:</Form.Label>
                          <Form.Control
                            type="date"
                            value={props.dateRange.startDate.split('/').join('-')}
                            onChange={(e) => {
                              const date = e.target.value.split('-').join('/');
                              props.onDateChange('startDate', date);
                            }}
                            size="sm"
                            style={{ fontSize: '0.85rem' }}
                          />
                        </Form.Group>

                        <Form.Group className="mb-0">
                          <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>End Date:</Form.Label>
                          <Form.Control
                            type="date"
                            value={props.dateRange.endDate.split('/').join('-')}
                            onChange={(e) => {
                              const date = e.target.value.split('-').join('/');
                              props.onDateChange('endDate', date);
                            }}
                            size="sm"
                            style={{ fontSize: '0.85rem' }}
                          />
                        </Form.Group>
                      </div>
                    )}
                  </div>
                )}

                {/* Coordinates Input Section */}
                <div className="nested-section mb-3">
                  <div
                    className="nested-section-header clickable"
                    onClick={() => toggleNestedSection('coordinates')}
                  >
                    <div className="d-flex align-items-center">
                      <span className="nested-section-icon">📍</span>
                      <span className="nested-section-title">Coordinate Entry</span>
                    </div>
                    <span className="nested-section-toggle">
                      {nestedSections.coordinates ? '▼' : '▶'}
                    </span>
                  </div>
                  {nestedSections.coordinates && (
                    <div className="nested-section-body">
                      <Form.Group className="mb-2">
                        <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Enter coordinates:</Form.Label>
                        <div className="d-flex">
                          <Form.Control
                            type="text"
                            placeholder="latitude, longitude"
                            size="sm"
                            style={{ fontSize: '0.85rem' }}
                            value={props.currentCoordinates || ''}
                            readOnly={true}
                          />
                          <button
                            className={`btn btn-sm ms-2 ${isPinningMode ? 'btn-danger' : 'btn-primary'}`}
                            onClick={() => {
                              const newMode = !isPinningMode;
                              console.log('Pin button clicked - current isPinningMode:', isPinningMode, 'newMode:', newMode);
                              console.log('nestedSections.coordinates:', nestedSections.coordinates);
                              setIsPinningMode(newMode);
                              // Toggle pin mode in parent component
                              if (props.onCoordinatePinModeToggle) {
                                console.log('Calling onCoordinatePinModeToggle with:', newMode);
                                props.onCoordinatePinModeToggle(newMode);
                              }
                            }}
                            title={isPinningMode ? 'Cancel pin placement' : 'Place pin on map'}
                          >
                            {isPinningMode ? '✕' : '📌'}
                          </button>
                        </div>
                        <small className={`d-block mt-1 ${isPinningMode ? 'text-warning' : 'text-muted'}`}>
                          {isPinningMode 
                            ? 'Click on map to place a pin at desired location' 
                            : 'Example: -26.2041, 28.0473 or click the pin button'}
                        </small>
                      </Form.Group>
                    </div>
                  )}
                </div>

                {/* Drawing Tools Section */}
                <div className="nested-section mb-0">
                  <div
                    className="nested-section-header clickable"
                    onClick={() => toggleNestedSection('drawingTools')}
                  >
                    <div className="d-flex align-items-center">
                      <span className="nested-section-icon">✏️</span>
                      <span className="nested-section-title">Drawing Tools</span>
                    </div>
                    <span className="nested-section-toggle">
                      {nestedSections.drawingTools ? '▼' : '▶'}
                    </span>
                  </div>
                  {nestedSections.drawingTools && (
                    <div className="nested-section-body">
                      <RegionSelector
                        onSearch={onSearch}
                        onDrawModeToggle={onDrawModeToggle}
                        isDrawingMode={props.isDrawingMode}
                        hasDrawnArea={props.hasDrawnArea}
                        onClearDrawnArea={onClearDrawnArea}
                        aoiMethod={props.aoiMethod}
                        onAOIMethodChange={onAOIMethodChange}
                        hasRegionalSelection={props.hasRegionalSelection}
                        onConfigureRegions={onConfigureRegions}
                        onClearRegionalSelection={onClearRegionalSelection}
                        onPredefinedPolygon={onPredefinedPolygon}
                        onBoundaryHighlight={props.onBoundaryHighlight}
                        onBoundaryRegionSelection={props.onBoundaryRegionSelection}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* AOI Preview Card - Only show when everything is complete - DISABLED for now */}
          {/* {isLayerSelectionComplete() && (localAoiPreviewData || props.aoiPreviewData) && (
            <AOIPreviewCard
              aoiData={localAoiPreviewData || props.aoiPreviewData}
              selectedLayers={props.selectedLayerNames}
              selectedBasemap={props.selectedBasemap}
              onDownload={handleDownloadAOI}
            />
          )} */}

          {/* Data Layers Card - Always visible for discovery */}
          <div className="sidebar-card">
            <div className="sidebar-card-header">
              <h5 className="sidebar-card-title">
                Data Layers
                {/* Show AOI badge when date range is complete but layers not selected */}
                {isDateRangeComplete() && !isLayerSelectionComplete() && (
                  <span style={{ 
                    color: '#28a745', 
                    marginLeft: '8px', 
                    fontSize: '0.8rem',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    padding: '2px 6px',
                    borderRadius: '3px',
                    border: '1px solid rgba(40, 167, 69, 0.3)'
                  }}>
                    🎯 Select for AOI Analysis
                  </span>
                )}
              </h5>
            </div>
            <div className="sidebar-card-body">
              {/* Show AOI-specific help when in AOI workflow */}
              {isDateRangeComplete() && !isLayerSelectionComplete() && (
                <div style={{ 
                  fontSize: '0.8rem', 
                  color: '#28a745', 
                  marginBottom: '12px',
                  padding: '8px',
                  backgroundColor: 'rgba(40, 167, 69, 0.1)',
                  borderRadius: '4px',
                  border: '1px solid rgba(40, 167, 69, 0.3)'
                }}>
                  🎯 <strong>AOI Analysis Ready:</strong> Select the data layers you want to analyze for your chosen area ({selectedRegions.ward || selectedRegions.municipality || selectedRegions.district || selectedRegions.provinceName}) and time period.
                </div>
              )}
              
              {/* Show general discovery help when not in AOI workflow */}
              {!isDateRangeComplete() && (
                <div style={{ 
                  fontSize: '0.8rem', 
                  color: '#b3d9ff', 
                  marginBottom: '12px',
                  padding: '8px',
                  backgroundColor: 'rgba(0, 123, 255, 0.1)',
                  borderRadius: '4px',
                  border: '1px solid rgba(0, 123, 255, 0.2)'
                }}>
                  🗂️ Explore available data layers. Complete the Area of Interest selection above to enable targeted analysis.
                </div>
              )}
              
              <DataLayers
                layers={props.layers}
                selectedLayerNames={props.selectedLayerNames}
                onLayerChange={props.onLayerChange}
                isLoading={props.isLoading}
                error={props.error}
                selectedBasemap={props.selectedBasemap}
                onBasemapChange={props.onBasemapChange}
              />
            </div>
          </div>

          {/* Service Details Card */}
          {/* <div className="sidebar-card">
            <div className="sidebar-card-header">
              <h5 className="sidebar-card-title">Layer Information</h5>
            </div>
            <div className="sidebar-card-body">
              <ServiceDetails
                selectedLayers={props.selectedLayerNames.reduce((acc, name) => {
                  acc[name] = true;
                  return acc;
                }, {} as Record<string, boolean>)}
              />
            </div>
          </div> */}

          {/* Actions Card - Only show when layers are selected */}
          {isLayerSelectionComplete() && (
            <div className="sidebar-card">
              <div className="sidebar-card-header">
                <h5 className="sidebar-card-title">Actions</h5>
              </div>
              <div className="sidebar-card-body">
                {/* Analysis Summary */}
                <div style={{ 
                  fontSize: '0.85rem', 
                  marginBottom: '16px',
                  padding: '12px',
                  backgroundColor: 'rgba(0, 123, 255, 0.05)',
                  borderRadius: '6px',
                  border: '1px solid rgba(0, 123, 255, 0.2)'
                }}>
                  <div style={{ color: '#0056b3', fontWeight: 'bold', marginBottom: '8px' }}>
                    🚀 Analysis Summary
                  </div>
                  
                  {/* Boundary Selection Summary */}
                  <div style={{ marginBottom: '8px' }}>
                    <strong>📍 Area:</strong> {' '}
                    {selectedRegions.ward ? `${selectedRegions.ward} (Ward)` :
                     selectedRegions.municipality ? `${selectedRegions.municipality} (Municipality)` :
                     selectedRegions.district ? `${selectedRegions.district} (District)` :
                     selectedRegions.provinceName ? `${selectedRegions.provinceName} (Province)` :
                     localAoiPreviewData?.name || 'Custom Area'}
                  </div>
                  
                  {/* Date Range Summary */}
                  <div style={{ marginBottom: '8px' }}>
                    <strong>📅 Period:</strong> {' '}
                    {props.dateRange.startDate && props.dateRange.endDate ? 
                      `${new Date(props.dateRange.startDate).toLocaleDateString()} - ${new Date(props.dateRange.endDate).toLocaleDateString()}` :
                      'Date range selected'}
                  </div>
                  
                  {/* Layers Summary */}
                  <div>
                    <strong>🗂️ Layers:</strong> {' '}
                    {props.selectedLayerNames.length > 0 ? 
                      `${props.selectedLayerNames.length} layer${props.selectedLayerNames.length !== 1 ? 's' : ''} selected` :
                      'No layers selected'}
                  </div>
                  
                  {props.selectedLayerNames.length > 0 && (
                    <div style={{ marginTop: '6px', fontSize: '0.75rem', color: '#6c757d' }}>
                      {props.selectedLayerNames.slice(0, 3).join(', ')}
                      {props.selectedLayerNames.length > 3 && ` + ${props.selectedLayerNames.length - 3} more`}
                    </div>
                  )}
                </div>
                <DataActions
                  onPreviewData={onPreviewData}
                  onDownloadData={onDownloadData}
                  onQueryTemporalData={hasTemporalLayers ? onQueryTemporalData : undefined}
                  temporalLayerName={getSelectedLayerName()}
                />
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Sidebar;