import axios from 'axios';
import { LayerDiscovery, TemporalDimension } from '../types/discovery';
import { API_CONFIG } from '../config';

export interface WMSLayer {
  name: string;
  title: string;
  abstract?: string;
  type?: string;
  bbox?: {
    SRS?: string;
    minx: number | string;
    miny: number | string;
    maxx: number | string;
    maxy: number | string;
  };
  queryable?: boolean;
  // Supported formats for the layer
  formats?: string[];
  // Temporal capabilities
  temporal?: TemporalDimension;
  // Remote layer properties
  isRemote?: boolean;
  serviceType?: string;
  remoteUrl?: string;
  url?: string;
}

/**
 * Fetch available WMS layers via backend capabilities endpoint.
 * Returns enhanced LayerDiscovery objects with full metadata.
 */
export const fetchAvailableLayers = async (): Promise<LayerDiscovery[]> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/capabilities`);
    let layers: any[] = [];

    if (Array.isArray(response.data)) {
      layers = response.data;
    } else {
      console.error('Unexpected response format:', response.data);
      return [];
    }

    // Transform to LayerDiscovery format
    return layers.map((layer: any): LayerDiscovery => ({
      name: layer.name || '',
      title: layer.title || layer.name || '',
      abstract: layer.abstract || '',
      attribution: layer.attribution,
      keywords: Array.isArray(layer.keywords) ? layer.keywords : [],
      bbox: {
        SRS: layer.bbox?.SRS,
        minx: layer.bbox?.minx?.toString(),
        miny: layer.bbox?.miny?.toString(),
        maxx: layer.bbox?.maxx?.toString(),
        maxy: layer.bbox?.maxy?.toString(),
      },
      style: layer.style || layer.defaultStyle || '',
      legendUrl: layer.legendUrl || `${API_CONFIG.OWS_BASE_URL}/legend?layer=${encodeURIComponent(layer.name || '')}&format=image/png`,
      formats: Array.isArray(layer.formats) ? layer.formats : ['image/png'],
      supports: {
        WMS: layer.supports?.WMS !== false,
        WFS: layer.supports?.WFS === true,
        WMTS: layer.supports?.WMTS === true,
      },
      // Pass through queryable property for feature info support
      queryable: layer.queryable === true,
      // Pass through temporal information if available
      temporal: layer.temporal,
    }));
  } catch (error) {
    console.error('Error fetching available WMS layers:', error);
    return [];
  }
};

/**
 * Fetch available WMS layers in legacy WMSLayer format.
 */
export const fetchAvailableWMSLayers = async (): Promise<WMSLayer[]> => {
  const discoveries = await fetchAvailableLayers();
  return discoveries.map(layerDiscoveryToWMSLayer);
};

/**
 * Convert LayerDiscovery to WMSLayer for backward compatibility.
 */
export const layerDiscoveryToWMSLayer = (discovery: LayerDiscovery): WMSLayer => ({
  name: discovery.name,
  title: discovery.title,
  abstract: discovery.abstract,
  type: discovery.supports.WMS ? 'WMS' : 'unknown',
  bbox: {
    SRS: discovery.bbox.SRS,
    minx: discovery.bbox.minx || '',
    miny: discovery.bbox.miny || '',
    maxx: discovery.bbox.maxx || '',
    maxy: discovery.bbox.maxy || '',
  },
  queryable: discovery.queryable || false,
  temporal: discovery.temporal,
});

/**
 * Fetch WMS layer styles and temporal info from backend.
 */
export const fetchLayerStyles = async (layerName: string): Promise<any> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/styles/` + encodeURIComponent(layerName));
    return response.data;
  } catch (error) {
    console.error(`Error fetching styles for layer ${layerName}:`, error);
    return null;
  }
};

/**
 * Fetch backend service metadata.
 */
export const fetchServiceMetadata = async (): Promise<any> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/metadata`);
    return response.data;
  } catch (error) {
    console.error('Error fetching service metadata:', error);
    throw error;
  }
};

/**
 * Fetch metadata for a specific layer.
 * Returns enhanced LayerDiscovery object with full metadata.
 */
export const fetchLayerMetadata = async (layerName: string): Promise<LayerDiscovery | null> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/layer-metadata/` + encodeURIComponent(layerName));
    const data = response.data;
    
    if (!data) return null;

    // Transform to LayerDiscovery format
    return {
      name: data.name || layerName,
      title: data.title || data.name || layerName,
      abstract: data.abstract || '',
      attribution: data.attribution,
      keywords: Array.isArray(data.keywords) ? data.keywords : [],
      bbox: {
        SRS: data.bbox?.SRS,
        minx: data.bbox?.minx?.toString(),
        miny: data.bbox?.miny?.toString(),
        maxx: data.bbox?.maxx?.toString(),
        maxy: data.bbox?.maxy?.toString(),
      },
      style: data.style || data.defaultStyle || '',
      legendUrl: data.legendUrl || `${API_CONFIG.OWS_BASE_URL}/legend?layer=${encodeURIComponent(layerName)}&format=image/png`,
      formats: Array.isArray(data.formats) ? data.formats : ['image/png'],
      supports: {
        WMS: data.supports?.WMS !== false,
        WFS: data.supports?.WFS === true,
        WMTS: data.supports?.WMTS === true,
      },
      // Pass through queryable property for feature info support
      queryable: data.queryable === true,
      // Pass through temporal information if available
      temporal: data.temporal,
    };
  } catch (error) {
    console.error(`Error fetching metadata for layer ${layerName}:`, error);
    return null;
  }
};

/**
 * Fetch features (GeoJSON) for a specific layer.
 */
export const fetchLayerFeatures = async (layerName: string, bbox?: string, startDate?: string, endDate?: string): Promise<any> => {
  try {
    const params: any = {
      typeName: layerName,
      outputFormat: 'application/json',
    };
    if (bbox) params.bbox = bbox;
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/features`, { params });
    return response.data;
  } catch (error) {
    console.error(`Error fetching features for layer ${layerName}:`, error);
    return null;
  }
};

/**
 * Fetch legend graphic for a given layer.
 */
export const fetchLegendGraphic = async (layerName: string, format: string = 'image/png'): Promise<Blob | null> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/legend`, {
      params: { layer: layerName, format },
      responseType: 'blob'
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching legend graphic for layer ${layerName}:`, error);
    return null;
  }
};

/**
 * Search for locations using backend geocoding or spatial search.
 */
export const searchLocations = async (query: string): Promise<any[]> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/search`, { params: { query } });
    return response.data;
  } catch (error) {
    console.error('Error searching locations:', error);
    return [];
  }
};

/**
 * Fetch comprehensive metadata for a specific layer including OGC metadata.
 */
export const fetchLayerMetadataDetailed = async (layerName: string): Promise<LayerDiscovery> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/layer-metadata/${encodeURIComponent(layerName)}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching detailed layer metadata:', error);
    throw error;
  }
};

/**
 * Fetch feature information for a specific point on queryable layers.
 */
export interface GetFeatureInfoParams {
  layers: string;
  queryLayers: string;
  x: number;
  y: number;
  width: number;
  height: number;
  bbox: string;
  srs?: string;
  infoFormat?: string;
  featureCount?: number;
}

export const fetchFeatureInfo = async (params: GetFeatureInfoParams): Promise<any> => {
  try {
    const queryParams = {
      layers: params.layers,
      query_layers: params.queryLayers,
      x: params.x.toString(),
      y: params.y.toString(),
      width: params.width.toString(),
      height: params.height.toString(),
      bbox: params.bbox,
      srs: params.srs || 'EPSG:4326',
      info_format: params.infoFormat || 'application/json',
      feature_count: (params.featureCount || 10).toString()
    };

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/feature-info`, { params: queryParams });
    return response.data;
  } catch (error) {
    console.error('Error fetching feature info:', error);
    throw error;
  }
};

/**
 * AOI Screenshot Generation Interface and Function
 */
export interface AOIScreenshotParams {
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  selectedLayers: string[];
  selectedBasemap?: string;
  dateRange?: {
    startDate?: string;
    endDate?: string;
  };
  dimensions?: {
    width: number;
    height: number;
  };
  format?: 'png' | 'jpeg';
  layerOpacities?: Record<string, number>;
}

/**
 * Generate AOI screenshot by calling the backend screenshot service.
 * Returns a blob URL that can be used as an image src.
 */
/**
 * Test if the backend screenshot endpoint is reachable
 */
export const testScreenshotEndpoint = async (): Promise<boolean> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/aoi-screenshot`, {
      params: {
        bbox: '0,0,1,1',
        layers: 'test',
        width: '100',
        height: '100'
      },
      timeout: 5000
    });
    return response.status === 200;
  } catch (error) {
    console.error('Screenshot endpoint test failed:', error);
    return false;
  }
};

export const generateAOIScreenshot = async (params: AOIScreenshotParams): Promise<string> => {
  console.log('generateAOIScreenshot called with params:', params);

  // First test if the endpoint is reachable
  console.log('Testing screenshot endpoint...');
  const endpointReachable = await testScreenshotEndpoint();
  console.log('Endpoint reachable:', endpointReachable);

  if (!endpointReachable) {
    throw new Error('Screenshot service is not available. Please check if the backend server is running.');
  }

  try {
    const {
      bounds,
      selectedLayers,
      selectedBasemap = 'osm:osm', // Default to OpenStreetMap
      dateRange,
      dimensions = { width: 800, height: 600 },
      format = 'png',
      layerOpacities = {}
    } = params;

    console.log('Extracted params:', {
      bounds,
      selectedLayers,
      selectedBasemap,
      dateRange,
      dimensions,
      format
    });

    // Construct bbox parameter (west,south,east,north)
    const bbox = `${bounds.west},${bounds.south},${bounds.east},${bounds.north}`;

    // For now, let's try with just the selected layers to debug
    // We can add basemap integration once we confirm the basic functionality works
    let layers: string;

    if (selectedLayers.length > 0) {
      layers = selectedLayers.join(',');
      console.log('Using selected layers only:', layers);
    } else {
      // If no layers selected, try with a simple basemap
      layers = selectedBasemap;
      console.log('No data layers selected, using basemap only:', layers);
    }

    // Validate that we have layers
    if (!layers || layers.trim() === '') {
      throw new Error('No valid layers for screenshot generation');
    }

    // Prepare query parameters
    const queryParams: any = {
      bbox,
      layers,
      width: dimensions.width.toString(),
      height: dimensions.height.toString(),
      format,
      srs: 'EPSG:4326',
      transparent: 'true' // Keep transparent to allow layer composition
    };

    // Add temporal parameter only if valid date range is provided
    if (dateRange?.startDate && dateRange?.endDate) {
      try {
        // Convert date range to WMS TIME parameter format
        // Handle different date formats (YYYY/MM/DD or YYYY-MM-DD)
        const normalizeDate = (dateStr: string) => {
          if (!dateStr || dateStr.trim() === '') {
            throw new Error('Empty date string');
          }

          // Handle different date formats
          let normalized = dateStr;

          // Check if it's in DD/YYYY/MM format and fix it
          const ddYyyyMmMatch = dateStr.match(/^(\d{1,2})\/(\d{4})\/(\d{1,2})$/);
          if (ddYyyyMmMatch) {
            const [, day, year, month] = ddYyyyMmMatch;
            normalized = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
          } else {
            // Handle YYYY/MM/DD format
            normalized = dateStr.replace(/\//g, '-');
          }

          const date = new Date(normalized);

          if (isNaN(date.getTime())) {
            throw new Error(`Invalid date format: ${dateStr}`);
          }

          return date.toISOString().split('T')[0];
        };

        const startDate = normalizeDate(dateRange.startDate);
        const endDate = normalizeDate(dateRange.endDate);
        queryParams.time = `${startDate}/${endDate}`;

        console.log('Temporal parameters added:', {
          original: { start: dateRange.startDate, end: dateRange.endDate },
          normalized: { start: startDate, end: endDate },
          timeParam: queryParams.time
        });
      } catch (error) {
        console.warn('Invalid date range provided, proceeding without temporal parameter:', error);
        // Continue without temporal parameter - screenshot will still be generated
      }
    } else {
      console.log('No date range provided, generating screenshot without temporal filtering');
    }

    console.log('Generating AOI screenshot with params:', queryParams);
    console.log('Request details:', {
      bounds,
      selectedLayers,
      layersString: layers,
      hasDateRange: !!(dateRange?.startDate && dateRange?.endDate),
      layerOpacities
    });

    console.log(`Making request to ${API_CONFIG.OWS_BASE_URL}/aoi-screenshot...`);

    // Make request to backend screenshot endpoint
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/aoi-screenshot`, {
      params: queryParams,
      responseType: 'blob',
      timeout: 30000 // 30 second timeout
    });

    console.log('Response received:', {
      status: response.status,
      contentType: response.headers['content-type'],
      dataSize: response.data.size
    });

    // Create blob URL for the image
    const imageBlob = new Blob([response.data], {
      type: response.headers['content-type'] || `image/${format}`
    });
    const imageUrl = URL.createObjectURL(imageBlob);

    console.log('AOI screenshot generated successfully');
    return imageUrl;

  } catch (error) {
    console.error('Error generating AOI screenshot:', error);

    if (axios.isAxiosError(error)) {
      if (error.response?.status === 400) {
        throw new Error('Invalid parameters for screenshot generation');
      } else if (error.response?.status === 404) {
        throw new Error('One or more selected layers not found');
      } else if (error.response?.status === 408 || error.code === 'ECONNABORTED') {
        throw new Error('Screenshot generation timed out. Please try with fewer layers or a smaller area.');
      }
    }

    throw new Error('Failed to generate screenshot. Please try again.');
  }
};