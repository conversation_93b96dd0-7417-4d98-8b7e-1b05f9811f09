import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Row, <PERSON>, <PERSON>, Badge, <PERSON><PERSON>, Spin<PERSON> } from 'react-bootstrap';
import { Download, FileText, Map, Database, Image, Package } from 'lucide-react';

interface DownloadOption {
  id: string;
  name: string;
  description: string;
  format: string;
  icon: React.ReactNode;
  category: 'vector' | 'raster' | 'data' | 'document';
  size?: string;
  compatibility: string[];
}

interface DownloadModalProps {
  show: boolean;
  onHide: () => void;
  selectedLayers: string[];
  aoiData: any;
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

const DownloadModal: React.FC<DownloadModalProps> = ({
  show,
  onHide,
  selectedLayers,
  aoiData,
  dateRange
}) => {
  const [selectedFormats, setSelectedFormats] = useState<string[]>([]);
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [includePreview, setIncludePreview] = useState(true);
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);

  const downloadOptions: DownloadOption[] = [
    // Vector Formats
    {
      id: 'shapefile',
      name: 'Shapefile',
      description: 'Industry standard vector format',
      format: 'SHP',
      icon: <Map size={20} />,
      category: 'vector',
      size: '~2-5 MB',
      compatibility: ['ArcGIS', 'QGIS', 'MapInfo', 'AutoCAD']
    },
    {
      id: 'geojson',
      name: 'GeoJSON',
      description: 'Web-friendly JSON format',
      format: 'JSON',
      icon: <FileText size={20} />,
      category: 'vector',
      size: '~1-3 MB',
      compatibility: ['Web browsers', 'Leaflet', 'OpenLayers', 'QGIS']
    },
    {
      id: 'kml',
      name: 'KML/KMZ',
      description: 'Google Earth compatible format',
      format: 'KML',
      icon: <Map size={20} />,
      category: 'vector',
      size: '~1-2 MB',
      compatibility: ['Google Earth', 'ArcGIS', 'QGIS']
    },
    {
      id: 'geopackage',
      name: 'GeoPackage',
      description: 'Modern SQLite-based format',
      format: 'GPKG',
      icon: <Database size={20} />,
      category: 'vector',
      size: '~2-4 MB',
      compatibility: ['QGIS', 'ArcGIS Pro', 'FME', 'GDAL']
    },
    
    // Raster Formats
    {
      id: 'geotiff',
      name: 'GeoTIFF',
      description: 'Georeferenced raster format',
      format: 'TIF',
      icon: <Image size={20} />,
      category: 'raster',
      size: '~10-50 MB',
      compatibility: ['ArcGIS', 'QGIS', 'ERDAS', 'ENVI']
    },
    {
      id: 'png',
      name: 'PNG with World File',
      description: 'Image with georeferencing',
      format: 'PNG',
      icon: <Image size={20} />,
      category: 'raster',
      size: '~5-20 MB',
      compatibility: ['Most GIS software', 'Web applications']
    },
    
    // Data Formats
    {
      id: 'csv',
      name: 'CSV',
      description: 'Tabular data with coordinates',
      format: 'CSV',
      icon: <FileText size={20} />,
      category: 'data',
      size: '~500 KB - 2 MB',
      compatibility: ['Excel', 'R', 'Python', 'Any spreadsheet software']
    },
    {
      id: 'excel',
      name: 'Excel Workbook',
      description: 'Multi-sheet Excel file',
      format: 'XLSX',
      icon: <FileText size={20} />,
      category: 'data',
      size: '~1-3 MB',
      compatibility: ['Microsoft Excel', 'LibreOffice Calc', 'Google Sheets']
    },
    
    // Document Formats
    {
      id: 'pdf_report',
      name: 'Analysis Report',
      description: 'Comprehensive PDF report',
      format: 'PDF',
      icon: <FileText size={20} />,
      category: 'document',
      size: '~2-10 MB',
      compatibility: ['Any PDF reader']
    },
    {
      id: 'complete_package',
      name: 'Complete Package',
      description: 'All formats in one ZIP file',
      format: 'ZIP',
      icon: <Package size={20} />,
      category: 'document',
      size: '~20-100 MB',
      compatibility: ['All GIS software']
    }
  ];

  const handleFormatToggle = (formatId: string) => {
    setSelectedFormats(prev => 
      prev.includes(formatId) 
        ? prev.filter(id => id !== formatId)
        : [...prev, formatId]
    );
  };

  const handleSelectAll = (category: string) => {
    const categoryFormats = downloadOptions
      .filter(option => option.category === category)
      .map(option => option.id);
    
    setSelectedFormats(prev => {
      const otherFormats = prev.filter(id => 
        !downloadOptions.find(opt => opt.id === id && opt.category === category)
      );
      return [...otherFormats, ...categoryFormats];
    });
  };

  const handleDownload = async () => {
    if (selectedFormats.length === 0) {
      alert('Please select at least one download format.');
      return;
    }

    setIsDownloading(true);
    setDownloadProgress(0);

    try {
      // Simulate download progress
      for (let i = 0; i <= 100; i += 10) {
        setDownloadProgress(i);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // Here you would make actual API calls to download the data
      console.log('Downloading formats:', selectedFormats);
      console.log('AOI Data:', aoiData);
      console.log('Selected Layers:', selectedLayers);
      console.log('Date Range:', dateRange);
      console.log('Include Metadata:', includeMetadata);
      console.log('Include Preview:', includePreview);

      // For now, just show success
      alert('Download completed successfully!');
      onHide();
      
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try again.');
    } finally {
      setIsDownloading(false);
      setDownloadProgress(0);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'vector': return <Map size={16} />;
      case 'raster': return <Image size={16} />;
      case 'data': return <Database size={16} />;
      case 'document': return <FileText size={16} />;
      default: return <Download size={16} />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'vector': return 'primary';
      case 'raster': return 'success';
      case 'data': return 'warning';
      case 'document': return 'info';
      default: return 'secondary';
    }
  };

  const groupedOptions = downloadOptions.reduce((acc, option) => {
    if (!acc[option.category]) {
      acc[option.category] = [];
    }
    acc[option.category].push(option);
    return acc;
  }, {} as Record<string, DownloadOption[]>);

  return (
    <Modal show={show} onHide={onHide} size="xl" centered>
      <Modal.Header style={{ backgroundColor: '#007bff', color: 'white' }}>
        <Modal.Title>
          <Download size={24} className="me-2" />
          Download Area of Interest Data
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body>
        {/* Download Summary */}
        <Alert variant="info" className="mb-4">
          <strong>Download Summary:</strong><br />
          • Selected Layers: {selectedLayers.length} layers<br />
          • Area: {aoiData?.area ? `${aoiData.area.toFixed(2)} km²` : 'Not specified'}<br />
          • Date Range: {dateRange.startDate} to {dateRange.endDate}<br />
          • AOI Method: {aoiData?.method || 'drawn'}
        </Alert>

        {/* Format Selection */}
        <h6 className="text-primary mb-3">Select Download Formats:</h6>
        
        {Object.entries(groupedOptions).map(([category, options]) => (
          <div key={category} className="mb-4">
            <div className="d-flex align-items-center justify-content-between mb-2">
              <h6 className="mb-0">
                <Badge bg={getCategoryColor(category)} className="me-2">
                  {getCategoryIcon(category)}
                </Badge>
                {category.charAt(0).toUpperCase() + category.slice(1)} Formats
              </h6>
              <Button
                variant="outline-secondary"
                size="sm"
                onClick={() => handleSelectAll(category)}
              >
                Select All
              </Button>
            </div>
            
            <Row>
              {options.map(option => (
                <Col md={6} lg={4} key={option.id} className="mb-3">
                  <Card 
                    className={`h-100 ${selectedFormats.includes(option.id) ? 'border-primary' : ''}`}
                    style={{ cursor: 'pointer' }}
                    onClick={() => handleFormatToggle(option.id)}
                  >
                    <Card.Body className="p-3">
                      <Form.Check
                        type="checkbox"
                        checked={selectedFormats.includes(option.id)}
                        onChange={() => handleFormatToggle(option.id)}
                        className="mb-2"
                        label=""
                      />
                      <div className="d-flex align-items-center mb-2">
                        {option.icon}
                        <strong className="ms-2">{option.name}</strong>
                        <Badge bg="secondary" className="ms-auto">{option.format}</Badge>
                      </div>
                      <p className="text-muted small mb-2">{option.description}</p>
                      {option.size && (
                        <small className="text-success">Est. size: {option.size}</small>
                      )}
                      <div className="mt-2">
                        <small className="text-muted">
                          Compatible with: {option.compatibility.slice(0, 2).join(', ')}
                          {option.compatibility.length > 2 && ` +${option.compatibility.length - 2} more`}
                        </small>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>
          </div>
        ))}

        {/* Additional Options */}
        <h6 className="text-primary mb-3">Additional Options:</h6>
        <Row>
          <Col md={6}>
            <Form.Check
              type="checkbox"
              checked={includeMetadata}
              onChange={(e) => setIncludeMetadata(e.target.checked)}
              label="Include metadata files"
              className="mb-2"
            />
            <small className="text-muted">Includes projection files, data dictionaries, and source information</small>
          </Col>
          <Col md={6}>
            <Form.Check
              type="checkbox"
              checked={includePreview}
              onChange={(e) => setIncludePreview(e.target.checked)}
              label="Include preview images"
              className="mb-2"
            />
            <small className="text-muted">Includes PNG thumbnails and overview maps</small>
          </Col>
        </Row>

        {/* Download Progress */}
        {isDownloading && (
          <div className="mt-4">
            <div className="d-flex align-items-center mb-2">
              <Spinner animation="border" size="sm" className="me-2" />
              <span>Preparing download... {downloadProgress}%</span>
            </div>
            <div className="progress">
              <div 
                className="progress-bar" 
                style={{ width: `${downloadProgress}%` }}
              ></div>
            </div>
          </div>
        )}
      </Modal.Body>
      
      <Modal.Footer>
        <div className="d-flex justify-content-between w-100">
          <div>
            <small className="text-muted">
              {selectedFormats.length} format{selectedFormats.length !== 1 ? 's' : ''} selected
            </small>
          </div>
          <div>
            <Button variant="secondary" onClick={onHide} disabled={isDownloading}>
              Cancel
            </Button>
            <Button 
              variant="primary" 
              onClick={handleDownload}
              disabled={selectedFormats.length === 0 || isDownloading}
              className="ms-2"
            >
              {isDownloading ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  Downloading...
                </>
              ) : (
                <>
                  <Download size={16} className="me-2" />
                  Download Selected
                </>
              )}
            </Button>
          </div>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default DownloadModal;
