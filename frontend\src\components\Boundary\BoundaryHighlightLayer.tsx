import React from 'react';
import { GeoJSON } from 'react-leaflet';
import L from 'leaflet';

interface BoundaryHighlightLayerProps {
  features: GeoJSON.Feature[];
  onFeatureClick?: (feature: GeoJSON.Feature) => void;
}

const BoundaryHighlightLayer: React.FC<BoundaryHighlightLayerProps> = ({
  features,
  onFeatureClick
}) => {
  // Don't render if no features
  if (!features || features.length === 0) {
    return null;
  }

  // Create GeoJSON collection from features
  const geoJsonData: GeoJSON.FeatureCollection = {
    type: 'FeatureCollection',
    features: features
  };

  const onEachFeature = (feature: GeoJSON.Feature, layer: L.Layer) => {
    // Add click handler
    if (onFeatureClick) {
      layer.on('click', () => {
        onFeatureClick(feature);
      });
    }

    // Add popup with boundary information
    const props = feature.properties || {};
    const popupContent = `
      <div style="font-size: 12px;">
        <strong>Administrative Boundary</strong><br/>
        ${props.adm1_en ? `<strong>Province:</strong> ${props.adm1_en}<br/>` : ''}
        ${props.adm2_en ? `<strong>District:</strong> ${props.adm2_en}<br/>` : ''}
        ${props.adm3_en ? `<strong>Municipality:</strong> ${props.adm3_en}<br/>` : ''}
        ${props.adm4_en ? `<strong>Ward:</strong> ${props.adm4_en}<br/>` : ''}
      </div>
    `;
    
    layer.bindPopup(popupContent);

    // Add hover effects
    layer.on('mouseover', function(this: L.Path) {
      this.setStyle({
        fillColor: '#ff7800',
        fillOpacity: 0.5,
        color: '#ff7800',
        weight: 3,
        opacity: 1
      });
    });

    layer.on('mouseout', function(this: L.Path) {
      this.setStyle({
        fillColor: '#3388ff',
        fillOpacity: 0.2,
        color: '#3388ff',
        weight: 2,
        opacity: 1
      });
    });
  };

  const pathOptions = {
    fillColor: '#3388ff',
    fillOpacity: 0.2,
    color: '#3388ff',
    weight: 2,
    opacity: 1,
    dashArray: '5, 5'
  };

  return (
    <GeoJSON
      key={`boundary-highlight-${features.length}`}
      data={geoJsonData}
      pathOptions={pathOptions}
      onEachFeature={onEachFeature}
    />
  );
};

export default BoundaryHighlightLayer;
