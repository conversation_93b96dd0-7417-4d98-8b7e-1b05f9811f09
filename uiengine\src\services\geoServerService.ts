import axios from 'axios';
import { parseStringPromise } from 'xml2js';
import dotenv from 'dotenv';
import https from 'https';
import { LayerDiscovery, ServiceMetadata } from '../types/discovery';
import { secureGet } from '../utils/secureRequest';
dotenv.config();

export interface FullDiscoveryResponse {
  layers: LayerDiscovery[];
  serviceMetadata?: ServiceMetadata;
}

const GEOSERVER_BASE = process.env.GEOSERVER_URL;
const GEOSERVER_WMS = `${GEOSERVER_BASE}/geonode/wms?service=WMS&version=1.3.0&request=GetCapabilities`;
const GEOSERVER_WFS = `${GEOSERVER_BASE}/geonode/wfs?service=WFS&version=2.0.0&request=GetCapabilities`;
const GEOSERVER_WMTS = `${GEOSERVER_BASE}/geonode/gwc/service/wmts?service=WMTS&version=1.0.0&request=GetCapabilities`;
const GEOSERVER_REST = `${GEOSERVER_BASE}/rest`;

const auth = {
  username: process.env.GEOSERVER_USERNAME || 'Ngoni',
  password: process.env.GEOSERVER_PASSWORD || 'geoserver'
};

export const getFullDiscovery = async (): Promise<LayerDiscovery[]> => {
  console.log('Starting layer discovery...');
  
  let wfsLayers: Set<string> = new Set();
  try {
    const wfsRes = await secureGet(GEOSERVER_WFS);
    const wfsParsed = await parseStringPromise(wfsRes.data);
    const featureTypes = wfsParsed['WFS_Capabilities']?.FeatureTypeList?.[0]?.FeatureType || [];
    featureTypes.forEach((ft: any) => {
      const name = ft.Name?.[0];
      if (name) wfsLayers.add(name);
    });
    console.log(`Found ${wfsLayers.size} WFS layers`);
  } catch (err) {
    console.warn('Could not retrieve WFS capabilities:', err);
  }

  let wmtsLayers: Set<string> = new Set();
  try {
    const wmtsRes = await secureGet(GEOSERVER_WMTS);
    const wmtsParsed = await parseStringPromise(wmtsRes.data);
    const contents = wmtsParsed.Capabilities?.Contents?.[0]?.Layer || [];
    contents.forEach((l: any) => {
      const identifier = l.Identifier?.[0];
      if (identifier) wmtsLayers.add(identifier);
    });
    console.log(`Found ${wmtsLayers.size} WMTS layers`);
  } catch (err) {
    console.warn('Could not retrieve WMTS capabilities:', err);
  }

  const xmlRes = await secureGet(GEOSERVER_WMS);
  const parsed = await parseStringPromise(xmlRes.data);
  
  // Safe property access
  if (!parsed['WMS_Capabilities']) {
    throw new Error('Invalid WMS response: missing WMS_Capabilities');
  }
  
  const capabilities = parsed['WMS_Capabilities'];
  if (!capabilities['Capability'] || !capabilities['Capability'][0]) {
    throw new Error('Invalid WMS response: missing Capability section');
  }
  
  const capability = capabilities['Capability'][0];
  const layers = capability['Layer']?.[0]?.['Layer'] || [];
  
  // Safe access to global formats
  const globalFormats = capability.Request?.[0]?.GetMap?.[0]?.Format?.map((f: any) => f.trim()) || ['image/png', 'image/jpeg'];
  
  console.log(`Found ${layers.length} WMS layers`);


  const result = await Promise.all(
    layers.map(async (layer: any) => {
      const name = layer.Name?.[0] || '';
      const title = layer.Title?.[0] || '';
      const abstract = layer.Abstract?.[0] || '';
      const keywords = (layer.KeywordList?.[0]?.Keyword || []).map((k: any) => k._ || k);
      const bboxNode = layer.BoundingBox?.[0]?.$ || {};
      const attribution = layer.Attribution?.[0]?.Title?.[0] || undefined;

      // Extract CRS information
      const crs = layer.CRS || layer.SRS || [];

      // Extract temporal dimensions
      let temporal: any = null;
      const dimensions = layer.Dimension || [];
      const timeDimension = dimensions.find((dim: any) =>
        dim.$.name?.toLowerCase() === 'time' || dim.$.name?.toLowerCase() === 'temporal'
      );
      if (timeDimension) {
        temporal = {
          name: timeDimension.$.name,
          units: timeDimension.$.units,
          unitSymbol: timeDimension.$.unitSymbol,
          default: timeDimension.$.default,
          multipleValues: timeDimension.$.multipleValues === 'true',
          nearestValue: timeDimension.$.nearestValue === 'true',
          current: timeDimension.$.current === 'true',
          extent: timeDimension._,
          values: timeDimension._ ? timeDimension._.split(',').map((v: string) => v.trim()) : []
        };
      }

      // Extract metadata URLs
      const metadataUrls = (layer.MetadataURL || []).map((url: any) => ({
        type: url.$.type,
        format: url.$.format,
        onlineResource: url.OnlineResource?.[0]?.$?.['xlink:href'] || url.OnlineResource?.[0]
      }));

      // Extract data URLs
      const dataUrls = (layer.DataURL || []).map((url: any) => ({
        format: url.$.format,
        onlineResource: url.OnlineResource?.[0]?.$?.['xlink:href'] || url.OnlineResource?.[0]
      }));

      // Extract feature list URLs
      const featureListUrls = (layer.FeatureListURL || []).map((url: any) => ({
        format: url.$.format,
        onlineResource: url.OnlineResource?.[0]?.$?.['xlink:href'] || url.OnlineResource?.[0]
      }));

      // Extract styles information
      const styles = (layer.Style || []).map((style: any) => ({
        name: style.Name?.[0] || '',
        title: style.Title?.[0],
        abstract: style.Abstract?.[0],
        legendUrl: style.LegendURL?.[0]?.OnlineResource?.[0]?.$?.['xlink:href'],
        styleSheetUrl: style.StyleSheetURL?.[0]?.OnlineResource?.[0]?.$?.['xlink:href'],
        styleUrl: style.StyleURL?.[0]?.OnlineResource?.[0]?.$?.['xlink:href']
      }));

      // Extract authority URLs
      const authorityUrls = (layer.AuthorityURL || []).map((url: any) => ({
        name: url.$.name,
        onlineResource: url.OnlineResource?.[0]?.$?.['xlink:href'] || url.OnlineResource?.[0]
      }));

      // Extract identifiers
      const identifiers = (layer.Identifier || []).map((id: any) => ({
        authority: id.$.authority,
        identifier: id._
      }));

      // Extract scale denominators and other properties
      const minScaleDenominator = layer.MinScaleDenominator?.[0] ? parseFloat(layer.MinScaleDenominator[0]) : undefined;
      const maxScaleDenominator = layer.MaxScaleDenominator?.[0] ? parseFloat(layer.MaxScaleDenominator[0]) : undefined;
      const queryable = layer.$.queryable === '1' || layer.$.queryable === 'true';
      const cascaded = layer.$.cascaded ? parseInt(layer.$.cascaded) : undefined;
      const opaque = layer.$.opaque === '1' || layer.$.opaque === 'true';
      const noSubsets = layer.$.noSubsets === '1' || layer.$.noSubsets === 'true';
      const fixedWidth = layer.$.fixedWidth ? parseInt(layer.$.fixedWidth) : undefined;
      const fixedHeight = layer.$.fixedHeight ? parseInt(layer.$.fixedHeight) : undefined;

      // Get default style
      let defaultStyle = '';
      try {
        const [workspace, layerNameOnly] = name.split(':');
        const urlWithWorkspace = `${GEOSERVER_REST}/layers/${workspace}:${layerNameOnly}.json`;

        let styleRes;
        try {
          styleRes = await secureGet(urlWithWorkspace);
        } catch (err) {
          if (typeof err === 'object' && err !== null && 'response' in err && (err as any).response?.status === 404) {
            const urlWithoutWorkspace = `${GEOSERVER_REST}/layers/${layerNameOnly}.json`;
            styleRes = await secureGet(urlWithoutWorkspace);
          } else {
            throw err;
          }
        }

        defaultStyle = styleRes.data.layer.defaultStyle?.name || '';
      } catch (e) {
        if (typeof e === 'object' && e !== null && 'response' in e) {
          const err = e as any;
          console.warn(`[Style Detection] Failed for ${name}:`, err.response?.status, err.response?.data || err.message);
        } else {
          console.warn(`[Style Detection] Failed for ${name}:`, e);
        }
      }

      // Use workspace-specific endpoint for legend URL generation
      // const legendUrl = `${GEOSERVER_BASE}/ows?SERVICE=WMS&REQUEST=GetLegendGraphic&LAYER=${name}&FORMAT=image/png`;
      const legendUrl = `${GEOSERVER_BASE}/geonode/wms?service=WMS&request=GetLegendGraphic&layer=${name}&format=image/png`;

      return {
        name,
        title,
        abstract,
        attribution,
        keywords,
        bbox: {
          SRS: bboxNode.CRS || bboxNode.SRS,
          minx: bboxNode.minx,
          miny: bboxNode.miny,
          maxx: bboxNode.maxx,
          maxy: bboxNode.maxy
        },
        style: defaultStyle,
        legendUrl,
        formats: globalFormats,
        supports: {
          WMS: true,
          WFS: false,
          WMTS: false
        },
        // Enhanced OGC metadata
        crs,
        temporal,
        metadataUrls,
        dataUrls,
        featureListUrls,
        styles,
        dimensions: temporal ? [temporal] : [],
        authorityUrls,
        identifiers,
        minScaleDenominator,
        maxScaleDenominator,
        queryable,
        cascaded,
        opaque,
        noSubsets,
        fixedWidth,
        fixedHeight
      };
    })
  );

  // TODO: Add service metadata as a separate endpoint if needed
  return result;
};
