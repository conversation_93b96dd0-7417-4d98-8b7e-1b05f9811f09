/**
 * Enhanced WMS proxy handler specifically for binary/image responses
 * 
 * This module provides specialized handling for WMS GetMap requests 
 * which return binary image data that needs to be properly streamed.
 */
const axios = require('axios');
const https = require('https');
const stream = require('stream');

// Create a custom HTTPS agent that ignores certificate validation errors
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

/**
 * Stream a WMS response from GeoServer with certificate validation bypassed
 * 
 * @param {string} url - The GeoServer URL
 * @param {object} params - The WMS parameters
 * @returns {Promise<object>} - Response object with headers and a readable stream
 */
async function streamSecureWmsRequest(url, params) {
  console.log(`🌐 Streaming WMS request to ${url}`);
  console.log(`🌐 WMS Params:`, JSON.stringify(params));

  try {
    const response = await axios({
      method: 'get',
      url: url,
      params: params,
      responseType: 'stream',
      httpsAgent
    });

    console.log(`🌐 WMS stream response received:`, {
      status: response.status,
      headers: response.headers,
    });

    return {
      status: response.status,
      headers: response.headers,
      data: response.data
    };
  } catch (error) {
    console.error(`🌐 WMS stream request failed:`, error.message);
    if (error.response) {
      console.error(`🌐 Status: ${error.response.status}`);
      console.error(`🌐 Headers:`, error.response.headers);
      
      // If GeoServer returned an error with XML content, convert to string for debugging
      if (error.response.data) {
        const chunks = [];
        await new Promise((resolve, reject) => {
          error.response.data.on('data', chunk => chunks.push(chunk));
          error.response.data.on('end', () => resolve());
          error.response.data.on('error', reject);
        });
        
        const buffer = Buffer.concat(chunks);
        console.error(`🌐 Error response body:`, buffer.toString('utf8'));
      }
    }
    throw error;
  }
}

module.exports = {
  streamSecureWmsRequest
};
