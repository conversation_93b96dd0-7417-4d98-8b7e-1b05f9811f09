<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GeoServer WMS Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    .test-case {
      margin-bottom: 30px;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 5px;
    }
    h2 {
      margin-top: 0;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .result {
      margin-top: 15px;
      min-height: 30px;
    }
    button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 15px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 14px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    .log {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      max-height: 200px;
      overflow-y: auto;
      font-family: monospace;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>GeoServer Connection Test</h1>
    <p>This page tests various ways to connect to the GeoServer to help diagnose connection issues.</p>
    
    <div class="test-case">
      <h2>Test 1: Direct GeoServer GetCapabilities</h2>
      <p>Tests a direct connection to the GeoServer WMS GetCapabilities endpoint.</p>
      <button id="test1">Run Test</button>
      <div id="test1-result" class="result">
        <div id="test1-log" class="log"></div>
      </div>
    </div>
    
    <div class="test-case">
      <h2>Test 2: UI Engine Proxy GetCapabilities</h2>
      <p>Tests connecting to the GeoServer through the UI Engine proxy.</p>
      <button id="test2">Run Test</button>
      <div id="test2-result" class="result">
        <div id="test2-log" class="log"></div>
      </div>
    </div>
    
    <div class="test-case">
      <h2>Test 3: Direct GeoServer GetMap</h2>
      <p>Tests retrieving an image directly from the GeoServer.</p>
      <button id="test3">Run Test</button>
      <div id="test3-result" class="result">
        <div id="test3-log" class="log"></div>
        <img id="test3-image" style="max-width: 100%; display: none;" />
      </div>
    </div>
    
    <div class="test-case">
      <h2>Test 4: UI Engine Proxy GetMap</h2>
      <p>Tests retrieving an image via the UI Engine proxy.</p>
      <button id="test4">Run Test</button>
      <div id="test4-result" class="result">
        <div id="test4-log" class="log"></div>
        <img id="test4-image" style="max-width: 100%; display: none;" />
      </div>
    </div>
  </div>

  <script>
    const geoserverUrl = 'https://*************/geoserver';
    const uiEngineUrl = 'http://localhost:3001/api';
    
    function log(id, message) {
      const logElement = document.getElementById(`${id}-log`);
      const time = new Date().toLocaleTimeString();
      logElement.innerHTML += `[${time}] ${message}\n`;
    }
    
    // Test 1: Direct GeoServer GetCapabilities
    document.getElementById('test1').addEventListener('click', async () => {
      const testId = 'test1';
      log(testId, 'Starting direct GetCapabilities test...');
      
      try {
        const url = `${geoserverUrl}/wms?service=WMS&version=1.3.0&request=GetCapabilities`;
        log(testId, `Requesting URL: ${url}`);
        
        const response = await fetch(url);
        log(testId, `Response status: ${response.status} ${response.statusText}`);
        
        if (!response.ok) {
          throw new Error(`HTTP error: ${response.status}`);
        }
        
        const contentType = response.headers.get('content-type');
        log(testId, `Content-Type: ${contentType}`);
        
        const text = await response.text();
        log(testId, `Received ${text.length} bytes`);
        log(testId, `First 100 chars: ${text.substring(0, 100)}...`);
        log(testId, '✓ Test completed successfully');
      } catch (error) {
        log(testId, `✗ Error: ${error.message}`);
        console.error('Test 1 error:', error);
      }
    });
    
    // Test 2: UI Engine Proxy GetCapabilities
    document.getElementById('test2').addEventListener('click', async () => {
      const testId = 'test2';
      log(testId, 'Starting proxy GetCapabilities test...');
      
      try {
        const url = `${uiEngineUrl}/ows/capabilities`;
        log(testId, `Requesting URL: ${url}`);
        
        const response = await fetch(url);
        log(testId, `Response status: ${response.status} ${response.statusText}`);
        
        if (!response.ok) {
          throw new Error(`HTTP error: ${response.status}`);
        }
        
        const contentType = response.headers.get('content-type');
        log(testId, `Content-Type: ${contentType}`);
        
        const data = await response.json();
        log(testId, `Received ${Object.keys(data).length} layers`);
        log(testId, `First layer: ${data[0]?.name || 'none'}`);
        log(testId, '✓ Test completed successfully');
      } catch (error) {
        log(testId, `✗ Error: ${error.message}`);
        console.error('Test 2 error:', error);
      }
    });
    
    // Test 3: Direct GeoServer GetMap
    document.getElementById('test3').addEventListener('click', async () => {
      const testId = 'test3';
      log(testId, 'Starting direct GetMap test...');
      
      try {
        // Using a sample layer - adjust as needed
        const url = `${geoserverUrl}/wms?service=WMS&version=1.3.0&request=GetMap&layers=geonode:africa_mosaic_optmised&styles=&width=256&height=256&format=image/png&transparent=true&bbox=-180,-90,180,90&srs=EPSG:4326`;
        log(testId, `Requesting URL: ${url}`);
        
        const response = await fetch(url);
        log(testId, `Response status: ${response.status} ${response.statusText}`);
        
        if (!response.ok) {
          throw new Error(`HTTP error: ${response.status}`);
        }
        
        const contentType = response.headers.get('content-type');
        log(testId, `Content-Type: ${contentType}`);
        
        const blob = await response.blob();
        log(testId, `Received ${blob.size} bytes`);
        
        const imgUrl = URL.createObjectURL(blob);
        const imgElement = document.getElementById('test3-image');
        imgElement.src = imgUrl;
        imgElement.style.display = 'block';
        
        log(testId, '✓ Test completed successfully');
      } catch (error) {
        log(testId, `✗ Error: ${error.message}`);
        console.error('Test 3 error:', error);
      }
    });
    
    // Test 4: UI Engine Proxy GetMap
    document.getElementById('test4').addEventListener('click', async () => {
      const testId = 'test4';
      log(testId, 'Starting proxy GetMap test...');
      
      try {
        // Using the proxy endpoint
        const url = `${uiEngineUrl}/ows/wms-proxy?service=WMS&version=1.3.0&request=GetMap&layers=geonode:africa_mosaic_optmised&styles=&width=256&height=256&format=image/png&transparent=true&bbox=-180,-90,180,90&srs=EPSG:4326`;
        log(testId, `Requesting URL: ${url}`);
        
        const response = await fetch(url);
        log(testId, `Response status: ${response.status} ${response.statusText}`);
        
        if (!response.ok) {
          throw new Error(`HTTP error: ${response.status}`);
        }
        
        const contentType = response.headers.get('content-type');
        log(testId, `Content-Type: ${contentType}`);
        
        const blob = await response.blob();
        log(testId, `Received ${blob.size} bytes`);
        
        const imgUrl = URL.createObjectURL(blob);
        const imgElement = document.getElementById('test4-image');
        imgElement.src = imgUrl;
        imgElement.style.display = 'block';
        
        log(testId, '✓ Test completed successfully');
      } catch (error) {
        log(testId, `✗ Error: ${error.message}`);
        console.error('Test 4 error:', error);
      }
    });
  </script>
</body>
</html>
