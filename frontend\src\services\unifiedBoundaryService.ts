import axios from 'axios';
import { API_CONFIG } from '../config';

// Unified Administrative Boundary Service
// Consolidates legacy administrativeBoundaryService and Interactive Boundary Filter functionality

export interface AdministrativeRegion {
  id: string;
  name: string;
  code?: string;
  properties?: Record<string, any>;
}

export interface BoundaryFilterResponse {
  features: GeoJSON.Feature[];
  totalCount: number;
  isPartial: boolean;
  bounds?: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  area?: number;
}

export interface FilterOption {
  value: string;
  label: string;
}

export interface BoundaryFilters {
  province?: string;
  district?: string;
  municipality?: string;
  ward?: string;
}

// Cache for administrative boundary data
const boundaryCache = new Map<string, any>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Administrative boundary layer configuration
const BOUNDARY_LAYER = 'geonode:south_africa_municipal_boundaries';

/**
 * Security: Sanitize CQL filter values to prevent injection
 */
export const sanitizeCQLValue = (value: string): string => {
  if (!value || typeof value !== 'string') return '';
  
  // Remove potentially dangerous characters and patterns
  return value
    .replace(/['"\\]/g, '') // Remove quotes and backslashes
    .replace(/[;&|]/g, '')  // Remove command separators  
    .replace(/[<>]/g, '')   // Remove comparison operators
    .replace(/\b(DROP|DELETE|INSERT|UPDATE|CREATE|ALTER|EXEC|EXECUTE)\b/gi, '') // Remove SQL keywords
    .trim()
    .slice(0, 100); // Limit length
};

/**
 * Build CQL filter string from boundary filters
 */
export const buildCQLFilter = (filters: BoundaryFilters): string => {
  const conditions: string[] = [];
  
  if (filters.province) {
    const sanitized = sanitizeCQLValue(filters.province);
    conditions.push(`adm1_en='${sanitized}'`);
  }
  
  if (filters.district) {
    const sanitized = sanitizeCQLValue(filters.district);
    conditions.push(`adm2_en='${sanitized}'`);
  }
  
  if (filters.municipality) {
    const sanitized = sanitizeCQLValue(filters.municipality);
    conditions.push(`adm3_en='${sanitized}'`);
  }
  
  if (filters.ward) {
    const sanitized = sanitizeCQLValue(filters.ward);
    conditions.push(`adm4_en='${sanitized}'`);
  }
  
  return conditions.join(' AND ');
};

/**
 * Calculate bounds from GeoJSON features
 */
export const calculateBounds = (features: GeoJSON.Feature[]): BoundaryFilterResponse['bounds'] => {
  if (!features.length) return undefined;
  
  let minLng = Infinity, maxLng = -Infinity;
  let minLat = Infinity, maxLat = -Infinity;
  
  features.forEach(feature => {
    if (feature.geometry && feature.geometry.type === 'Polygon') {
      const coords = (feature.geometry as any).coordinates[0];
      coords.forEach((coord: number[]) => {
        const [lng, lat] = coord;
        minLng = Math.min(minLng, lng);
        maxLng = Math.max(maxLng, lng);
        minLat = Math.min(minLat, lat);
        maxLat = Math.max(maxLat, lat);
      });
    }
  });
  
  return {
    north: maxLat,
    south: minLat,
    east: maxLng,
    west: minLng
  };
};

/**
 * Estimate area from bounds (rough calculation)
 */
export const estimateArea = (bounds?: BoundaryFilterResponse['bounds']): number => {
  if (!bounds) return 0;
  
  // Simple area estimation in km²
  const latDiff = bounds.north - bounds.south;
  const lngDiff = bounds.east - bounds.west;
  
  // Convert degrees to km (rough approximation)
  const latKm = latDiff * 111; // 1 degree latitude ≈ 111 km
  const lngKm = lngDiff * 111 * Math.cos((bounds.north + bounds.south) / 2 * Math.PI / 180);
  
  return Math.abs(latKm * lngKm);
};

/**
 * Load provinces from the administrative boundaries layer
 */
export const loadProvinces = async (): Promise<AdministrativeRegion[]> => {
  const cacheKey = 'provinces';
  const cached = boundaryCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log('Using cached provinces data');
    return cached.data;
  }

  try {
    console.log('🔄 Loading provinces from WFS...');
    
    const params = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: BOUNDARY_LAYER,
      maxFeatures: 50, // Limit for provinces
      outputFormat: 'application/json',
      propertyName: 'adm1_id,adm1_en,adm1_pcode' // Only get province fields
    };

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
      params,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    const geojson = response.data;
    if (geojson && Array.isArray(geojson.features)) {
      // Extract unique provinces
      const provinceMap = new Map<string, AdministrativeRegion>();
      
      geojson.features.forEach((feature: any) => {
        const props = feature.properties;
        const id = props.adm1_id;
        const name = props.adm1_en;
        const code = props.adm1_pcode;
        
        if (id && name && !provinceMap.has(id)) {
          provinceMap.set(id, {
            id,
            name,
            code,
            properties: props
          });
        }
      });

      const provinces = Array.from(provinceMap.values());
      provinces.sort((a, b) => a.name.localeCompare(b.name));
      
      console.log(`✅ Loaded ${provinces.length} provinces`);
      
      // Cache the result
      boundaryCache.set(cacheKey, {
        data: provinces,
        timestamp: Date.now()
      });
      
      return provinces;
    } else {
      throw new Error('Invalid GeoJSON response for provinces');
    }
  } catch (error) {
    console.error('Failed to load provinces:', error);
    
    // Return fallback data
    console.warn('Using fallback provinces data');
    const fallbackProvinces: AdministrativeRegion[] = [
      { id: 'EC', name: 'Eastern Cape', code: 'ZA-EC' },
      { id: 'FS', name: 'Free State', code: 'ZA-FS' },
      { id: 'GT', name: 'Gauteng', code: 'ZA-GT' },
      { id: 'KZN', name: 'KwaZulu-Natal', code: 'ZA-KZN' },
      { id: 'LP', name: 'Limpopo', code: 'ZA-LP' },
      { id: 'MP', name: 'Mpumalanga', code: 'ZA-MP' },
      { id: 'NC', name: 'Northern Cape', code: 'ZA-NC' },
      { id: 'NW', name: 'North West', code: 'ZA-NW' },
      { id: 'WC', name: 'Western Cape', code: 'ZA-WC' }
    ];
    
    return fallbackProvinces;
  }
};

/**
 * Load districts for a specific province
 */
export const loadDistricts = async (provinceId?: string): Promise<AdministrativeRegion[]> => {
  const cacheKey = `districts-${provinceId || 'all'}`;
  const cached = boundaryCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log('Using cached districts data');
    return cached.data;
  }

  try {
    console.log('🔄 Loading districts from WFS...', provinceId ? `for province ${provinceId}` : 'all districts');
    
    const params: any = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: BOUNDARY_LAYER,
      maxFeatures: 999,
      outputFormat: 'application/json',
      propertyName: 'adm1_id,adm2_id,adm2_en,adm2_pcode'
    };

    // Add CQL filter for province if provided
    if (provinceId) {
      params.CQL_FILTER = `adm1_id='${sanitizeCQLValue(provinceId)}'`;
    }

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
      params,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    const geojson = response.data;
    if (geojson && Array.isArray(geojson.features)) {
      // Extract unique districts
      const districtMap = new Map<string, AdministrativeRegion>();
      
      geojson.features.forEach((feature: any) => {
        const props = feature.properties;
        const id = props.adm2_id;
        const name = props.adm2_en;
        const code = props.adm2_pcode;
        const provinceCode = props.adm1_id;
        
        // Filter by province if specified
        if (provinceId && provinceCode !== provinceId) return;
        
        if (id && name && !districtMap.has(id)) {
          districtMap.set(id, {
            id,
            name,
            code,
            properties: { ...props, isMetro: !id.startsWith('DC') }
          });
        }
      });

      const districts = Array.from(districtMap.values());
      
      // Sort with metros first, then alphabetically
      districts.sort((a, b) => {
        const aIsMetro = !a.id.startsWith('DC');
        const bIsMetro = !b.id.startsWith('DC');
        
        if (aIsMetro && !bIsMetro) return -1;
        if (!aIsMetro && bIsMetro) return 1;
        return a.name.localeCompare(b.name);
      });
      
      console.log(`✅ Loaded ${districts.length} districts`);
      
      // Cache the result
      boundaryCache.set(cacheKey, {
        data: districts,
        timestamp: Date.now()
      });
      
      return districts;
    } else {
      throw new Error('Invalid GeoJSON response for districts');
    }
  } catch (error) {
    console.error('Failed to load districts:', error);
    return [];
  }
};

/**
 * Load municipalities for a specific province/district
 */
export const loadMunicipalities = async (
  provinceName?: string, 
  districtCode?: string
): Promise<AdministrativeRegion[]> => {
  const cacheKey = `municipalities-${provinceName || 'all'}-${districtCode || 'all'}`;
  const cached = boundaryCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log('Using cached municipalities data');
    return cached.data;
  }

  try {
    console.log('🔄 Loading municipalities from WFS...', { provinceName, districtCode });
    
    const params: any = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: BOUNDARY_LAYER,
      maxFeatures: 999,
      outputFormat: 'application/json',
      propertyName: 'adm1_en,adm2_id,adm2_en,adm2_pcode'
    };

    // Build CQL filter
    const filters: string[] = [];
    if (provinceName) {
      filters.push(`adm1_en='${sanitizeCQLValue(provinceName)}'`);
    }
    if (districtCode) {
      filters.push(`adm2_id='${sanitizeCQLValue(districtCode)}'`);
    }
    
    if (filters.length > 0) {
      params.CQL_FILTER = filters.join(' AND ');
    }

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
      params,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    const geojson = response.data;
    console.log('🔍 Municipality response structure:', {
      hasData: !!geojson,
      type: typeof geojson,
      keys: geojson ? Object.keys(geojson) : [],
      hasFeatures: geojson?.features ? true : false,
      featuresType: geojson?.features ? typeof geojson.features : 'undefined',
      featuresLength: Array.isArray(geojson?.features) ? geojson.features.length : 'not array'
    });
    
    if (geojson && Array.isArray(geojson.features)) {
      // Extract unique municipalities
      const municipalityMap = new Map<string, AdministrativeRegion>();
      
      geojson.features.forEach((feature: any) => {
        const props = feature.properties;
        const id = props.adm2_id;
        const name = props.adm2_en;
        const code = props.adm2_pcode;
        
        if (id && name && !municipalityMap.has(id)) {
          municipalityMap.set(id, {
            id,
            name,
            code,
            properties: props
          });
        }
      });

      const municipalities = Array.from(municipalityMap.values());
      municipalities.sort((a, b) => a.name.localeCompare(b.name));
      
      console.log(`✅ Loaded ${municipalities.length} municipalities`);
      
      // Cache the result
      boundaryCache.set(cacheKey, {
        data: municipalities,
        timestamp: Date.now()
      });
      
      return municipalities;
    } else {
      console.error('Invalid municipality response structure:', {
        responseData: geojson,
        hasFeatures: !!geojson?.features,
        featuresType: typeof geojson?.features
      });
      throw new Error(`Invalid GeoJSON response for municipalities. Expected features array, got: ${typeof geojson?.features}`);
    }
  } catch (error) {
    console.error('Failed to load municipalities:', error);
    return [];
  }
};

/**
 * Load wards for a specific municipality
 */
export const loadWards = async (municipalityCode?: string): Promise<AdministrativeRegion[]> => {
  const cacheKey = `wards-${municipalityCode || 'all'}`;
  const cached = boundaryCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log('Using cached wards data');
    return cached.data;
  }

  try {
    console.log('🔄 Loading wards from WFS...', municipalityCode);
    
    const params: any = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: BOUNDARY_LAYER,
      maxFeatures: 999,
      outputFormat: 'application/json',
      propertyName: 'adm2_id,adm3_id,adm3_en,adm3_pcode'
    };

    // Add CQL filter for municipality if provided
    if (municipalityCode) {
      params.CQL_FILTER = `adm2_id='${sanitizeCQLValue(municipalityCode)}'`;
    }

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
      params,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    const geojson = response.data;
    if (geojson && Array.isArray(geojson.features)) {
      // Extract unique wards
      const wardMap = new Map<string, AdministrativeRegion>();
      
      geojson.features.forEach((feature: any) => {
        const props = feature.properties;
        const id = props.adm3_id;
        const name = props.adm3_en;
        const code = props.adm3_pcode;
        
        if (id && name && !wardMap.has(id)) {
          wardMap.set(id, {
            id,
            name,
            code,
            properties: props
          });
        }
      });

      const wards = Array.from(wardMap.values());
      wards.sort((a, b) => a.name.localeCompare(b.name));
      
      console.log(`✅ Loaded ${wards.length} wards`);
      
      // Cache the result
      boundaryCache.set(cacheKey, {
        data: wards,
        timestamp: Date.now()
      });
      
      return wards;
    } else {
      throw new Error('Invalid GeoJSON response for wards');
    }
  } catch (error) {
    console.error('Failed to load wards:', error);
    return [];
  }
};

/**
 * Get filtered boundary features for map display
 */
export const getFilteredBoundaryFeatures = async (
  filters: BoundaryFilters
): Promise<BoundaryFilterResponse> => {
  try {
    console.log('🗺️ Loading boundary features for map display...', filters);
    
    // Build CQL filter
    const cqlFilter = buildCQLFilter(filters);
    
    console.log('🔍 CQL Filter generated:', cqlFilter);
    
    if (!cqlFilter) {
      console.log('⚠️ No CQL filter generated, returning empty results');
      return { features: [], totalCount: 0, isPartial: false };
    }
    
    const params = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: BOUNDARY_LAYER,
      outputFormat: 'application/json',
      maxFeatures: 1000,
      CQL_FILTER: cqlFilter
    };

    console.log('🌐 WFS Request params:', params);
    
    // Convert params to string-only for URL construction
    const urlParams = {
      ...params,
      maxFeatures: params.maxFeatures.toString()
    };
    console.log('🌐 Full request URL:', `${API_CONFIG.OWS_BASE_URL}?${new URLSearchParams(urlParams).toString()}`);

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
      params,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    const geojson = response.data;
    console.log('🔍 Boundary API response status:', response.status);
    console.log('🔍 Boundary API response type:', typeof geojson);
    console.log('🔍 Boundary API response keys:', geojson ? Object.keys(geojson) : 'null');
    
    if (geojson && Array.isArray(geojson.features)) {
      const features = geojson.features;
      const bounds = calculateBounds(features);
      const area = estimateArea(bounds);
      
      console.log(`✅ Loaded ${features.length} boundary features for map`);
      
      return {
        features,
        totalCount: features.length,
        isPartial: features.length >= 1000,
        bounds,
        area
      };
    } else {
      console.error('❌ Invalid GeoJSON response structure:', {
        hasData: !!geojson,
        dataType: typeof geojson,
        hasFeatures: geojson && 'features' in geojson,
        featuresType: geojson && geojson.features ? typeof geojson.features : 'undefined',
        isArray: geojson && Array.isArray(geojson.features),
        sampleData: geojson ? JSON.stringify(geojson).substring(0, 200) + '...' : 'null'
      });
      throw new Error('Invalid GeoJSON response for boundary features');
    }
  } catch (error) {
    console.error('Failed to load boundary features:', error);
    return { features: [], totalCount: 0, isPartial: false };
  }
};

/**
 * Debounced version of getFilteredBoundaryFeatures for real-time filtering
 */
let debouncedTimer: NodeJS.Timeout | null = null;

export const debouncedGetFilteredBoundaryFeatures = (
  filters: BoundaryFilters,
  callback: (result: BoundaryFilterResponse) => void,
  delay: number = 300
) => {
  if (debouncedTimer) {
    clearTimeout(debouncedTimer);
  }
  
  debouncedTimer = setTimeout(async () => {
    try {
      const result = await getFilteredBoundaryFeatures(filters);
      callback(result);
    } catch (error) {
      console.error('Debounced boundary filter failed:', error);
      callback({ features: [], totalCount: 0, isPartial: false });
    }
  }, delay);
};

/**
 * Get the exact geometry of a selected boundary for precise AOI clipping
 */
export const getSelectedBoundaryGeometry = async (
  filters: BoundaryFilters
): Promise<{
  geometry: GeoJSON.Geometry | null;
  bounds: BoundaryFilterResponse['bounds'];
  feature: GeoJSON.Feature | null;
}> => {
  try {
    console.log('🎯 Getting exact geometry for selected boundary...', filters);
    
    // Build CQL filter for the specific selection
    const cqlFilter = buildCQLFilter(filters);
    
    if (!cqlFilter) {
      console.log('⚠️ No boundary selected for geometry extraction');
      return { geometry: null, bounds: undefined, feature: null };
    }
    
    const params = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: BOUNDARY_LAYER,
      outputFormat: 'application/json',
      maxFeatures: 1, // We only need the specific selected boundary
      CQL_FILTER: cqlFilter
    };

    console.log('🌐 Fetching boundary geometry with filter:', cqlFilter);
    console.log('🌐 Full request URL:', `${API_CONFIG.OWS_BASE_URL}?${new URLSearchParams(params as any).toString()}`);

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
      params,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    console.log('🔍 Boundary geometry API response:', {
      status: response.status,
      dataType: typeof response.data,
      hasFeatures: response.data?.features ? true : false,
      featureCount: response.data?.features?.length || 0
    });

    const geojson = response.data;

    if (geojson && Array.isArray(geojson.features) && geojson.features.length > 0) {
      const feature = geojson.features[0]; // Get the first (and should be only) feature
      const geometry = feature.geometry;
      const bounds = calculateBounds([feature]);

      console.log(`✅ Retrieved geometry for boundary: ${geometry.type}`, {
        coordinates: geometry.coordinates ? 'present' : 'missing',
        coordinatesLength: (geometry as any).coordinates?.length,
        bounds,
        featureProperties: Object.keys(feature.properties || {})
      });

      return {
        geometry,
        bounds,
        feature
      };
    } else {
      console.warn('❌ No boundary feature found for the selected filters:', {
        cqlFilter,
        responseData: geojson
      });
      return { geometry: null, bounds: undefined, feature: null };
    }
  } catch (error) {
    console.error('Failed to get boundary geometry:', error);
    return { geometry: null, bounds: undefined, feature: null };
  }
};

/**
 * Get available boundary values for dropdown population
 */
export const getAvailableBoundaryValues = async (
  level: 'provinces' | 'districts' | 'municipalities' | 'wards',
  parentFilters?: { province?: string; district?: string; municipality?: string }
): Promise<FilterOption[]> => {
  try {
    console.log(`🔍 Loading ${level} options...`, parentFilters);
    
    let regions: AdministrativeRegion[] = [];
    
    switch (level) {
      case 'provinces':
        regions = await loadProvinces();
        break;
      case 'districts':
        regions = await loadDistricts(parentFilters?.province);
        break;
      case 'municipalities':
        regions = await loadMunicipalities(
          parentFilters?.province,
          parentFilters?.district
        );
        break;
      case 'wards':
        regions = await loadWards(parentFilters?.municipality);
        break;
    }
    
    const options: FilterOption[] = regions.map(region => ({
      value: region.name,
      label: region.name
    }));
    
    console.log(`✅ Loaded ${options.length} ${level} options`);
    return options;
  } catch (error) {
    console.error(`Failed to load ${level} options:`, error);
    return [];
  }
};
