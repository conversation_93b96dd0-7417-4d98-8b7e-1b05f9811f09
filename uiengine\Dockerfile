FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (including devDependencies for build)
RUN npm install

# Copy source code
COPY . .

# Build TypeScript to JavaScript
RUN npm run build

# Create cache and logs directories with correct permissions
RUN mkdir -p /app/cache /app/logs \
    && chown -R node:node /app/cache /app/logs

# Remove devDependencies to reduce image size
RUN npm prune --production

USER node

EXPOSE 3001

CMD ["npm", "start"]
