/**
 * GeoServer connection validator
 * 
 * This module validates the connection to GeoServer on server startup
 * and logs appropriate warnings if the connection fails.
 */
const { secureGet } = require('./secureRequest');

/**
 * Validates the connection to GeoServer
 * @param {string} geoserverUrl - URL of the GeoServer instance
 * @returns {Promise<boolean>} - True if connection is successful, false otherwise
 */
async function validateGeoServerConnection(geoserverUrl) {
  if (!geoserverUrl) {
    console.warn('No GeoServer URL provided for validation');
    return false;
  }

  try {
    console.log(`[Startup] Validating connection to GeoServer: ${geoserverUrl}`);
    const wmsUrl = `${geoserverUrl}/wms?service=WMS&version=1.3.0&request=GetCapabilities`;
    
    const response = await secureGet(wmsUrl, { timeout: 5000 });
    
    if (response.status === 200) {
      console.log('[Startup] ✅ Successfully connected to GeoServer with certificate validation bypass');
      return true;
    } else {
      console.warn(`[Startup] ⚠️ GeoServer connection validation returned status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.error('[Startup] ❌ Failed to connect to GeoServer:', error.message);
    console.error('[Startup] 🔍 Make sure GeoServer is running and the URL is correct.');
    console.error('[Startup] 🔒 Certificate validation is bypassed, but the server may still be unreachable.');
    return false;
  }
}

module.exports = {
  validateGeoServerConnection
};
