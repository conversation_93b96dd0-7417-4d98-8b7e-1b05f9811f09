// Map component for SANSA Flood Monitoring
import React, { useEffect, useState, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, TileLayer, WMSTileLayer,
  useMap, Popup, Marker, Polygon, LayerGroup, useMapEvents
} from 'react-leaflet';
import L from 'leaflet';
import 'leaflet-draw'; // Import leaflet-draw JavaScript
import { fetchAvailableWMSLayers, WMSLayer, fetchFeatureInfo, GetFeatureInfoParams } from '../../services/geoserverService';
import { discoverLayers } from '../../services/discoveryService';
import { fetchFloodRiskData, fetchLayerData } from '../../services/mapService';
import { API_CONFIG } from '../../config';
import MapLegendPanel from './MapLegend';
import LoadingOverlay from './LoadingOverlay';
import FeatureInfoPopup, { FeatureInfo } from './FeatureInfoPopup';
import TemporalSelectionModal from '../AOI/TemporalSelectionModal';
import PinAreaSelectionModal from '../Pin/PinAreaSelectionModal';
import BoundaryHighlightLayer from '../Boundary/BoundaryHighlightLayer';
import aoiDebugger from '../../utils/aoiClippingDebugger';
import { Layers } from 'lucide-react';
import './MapComponent.css';

/**
 * Convert GeoJSON geometry to WKT format for CQL filters
 */
const convertGeoJSONToWKT = (geometry: GeoJSON.Geometry): string => {
  if (geometry.type === 'Polygon') {
    const coordinates = geometry.coordinates[0];
    const coordPairs = coordinates.map((coord: number[]) => `${coord[0]} ${coord[1]}`).join(', ');
    return `POLYGON((${coordPairs}))`;
  } else if (geometry.type === 'MultiPolygon') {
    const polygons = geometry.coordinates.map((polygon: number[][][]) => {
      const coordPairs = polygon[0].map((coord: number[]) => `${coord[0]} ${coord[1]}`).join(', ');
      return `(${coordPairs})`;
    }).join(', ');
    return `MULTIPOLYGON((${polygons}))`;
  }
  
  throw new Error(`Unsupported geometry type for WKT conversion: ${geometry.type}`);
};

interface MapComponentProps {
  selectedLayerNames: string[];
  dateRange: {
    startDate: string;
    endDate: string;
  };
  selectedTime?: string; // Add selectedTime prop for temporal layers
  onDrawComplete: (layers: any) => void;
  // AOI functionality
  isDrawingMode: boolean;
  onDrawModeChange: (isDrawing: boolean) => void;
  onAOIComplete: (aoiData: any, dateRange: any) => void;
  sidebarCollapsed?: boolean; // Add sidebarCollapsed prop for map resize
  // Basemap functionality
  selectedBasemap?: string;
  onBasemapChange?: (basemapName: string) => void;
  // Legend user mode
  legendUserMode?: 'simple' | 'advanced';
  onLegendUserModeChange?: (mode: 'simple' | 'advanced') => void;
  // Legend panel visibility
  showLegendPanel?: boolean;
  onCloseLegendPanel?: () => void;
  // Coordinate functionality
  isCoordinatePinMode?: boolean;
  onCoordinateSelected?: (latlng: {lat: number, lng: number}) => void;
  // AOI Preview functionality
  onAOIPreview?: (aoiData: any) => void;
  // Boundary highlighting
  highlightedBoundaries?: GeoJSON.Feature[];
  // AOI clipping
  aoiData?: {
    type: 'administrative' | 'drawn' | 'pin';
    bounds?: {
      north: number;
      south: number;
      east: number;
      west: number;
    };
    coordinates?: any;
    // Add geometry for precise clipping
    geometry?: GeoJSON.Geometry;
    feature?: GeoJSON.Feature;
  };
}

const BoundsCapture = ({ onBoundsChange }: { onBoundsChange: (bounds: any) => void }) => {
  const map = useMap();

  useEffect(() => {
    if (!map) return;
    const updateBounds = () => onBoundsChange(map.getBounds());
    map.on('moveend', updateBounds);
    updateBounds();
    return () => {
      map.off('moveend', updateBounds);
    };
  }, [map, onBoundsChange]);

  return null;
};

const MapRecentre = ({ trigger, center, zoom }: { trigger: boolean, center: [number, number], zoom: number }) => {
  const map = useMap();
  useEffect(() => {
    if (trigger) map.setView(center, zoom);
  }, [trigger, center, zoom, map]);
  return null;
};

// Map click handler for feature info and coordinate pin
const MapClickHandler = ({
  wmsLayers,
  onFeatureInfoClick,
  isDrawingMode,
  isCoordinatePinMode,
  onCoordinateSelected,
  setPinCoordinates,
  onPinPlaced
}: {
  wmsLayers: WMSLayer[];
  onFeatureInfoClick: (event: any, queryableLayers: WMSLayer[]) => void;
  isDrawingMode: boolean;
  isCoordinatePinMode?: boolean;
  onCoordinateSelected?: (latlng: {lat: number, lng: number}) => void;
  setPinCoordinates?: (latlng: {lat: number, lng: number} | null) => void;
  onPinPlaced?: (latlng: {lat: number, lng: number}) => void;
}) => {
  useMapEvents({
    click: (event) => {
      // Handle coordinate pin mode with highest priority
      if (isCoordinatePinMode) {
        // Update local pin coordinates for marker display
        if (setPinCoordinates) {
          setPinCoordinates(event.latlng);
        }
        
        // Call the handler passed from the parent component
        if (onCoordinateSelected) {
          onCoordinateSelected(event.latlng);
        }

        // Trigger pin area selection modal
        if (onPinPlaced) {
          onPinPlaced(event.latlng);
        }
        return;
      }
      
      // Don't handle feature info clicks when in drawing mode
      if (isDrawingMode) {
        return;
      }

      // Safety check: ensure wmsLayers is initialized and is an array
      if (!wmsLayers || !Array.isArray(wmsLayers)) {
        console.warn('wmsLayers not initialized yet, skipping feature info click');
        return;
      }

      // Filter for queryable layers that are currently visible
      const queryableLayers = wmsLayers.filter(layer => layer.queryable === true);

      if (queryableLayers.length > 0) {
        onFeatureInfoClick(event, queryableLayers);
      }
    }
  });

  return null;
};

// Component to handle map resize when sidebar state changes
const MapResizeHandler = ({ sidebarCollapsed }: { sidebarCollapsed?: boolean }) => {
  const map = useMap();

  useEffect(() => {
    if (map) {
      // Delay the resize to allow CSS transition to complete
      const timer = setTimeout(() => {
        map.invalidateSize();
      }, 350); // Slightly longer than CSS transition (300ms)

      return () => clearTimeout(timer);
    }
  }, [sidebarCollapsed, map]);

  return null;
};

// Drawing controller to programmatically start drawing
const DrawingController = ({
  isDrawingMode,
  onCreated
}: {
  isDrawingMode: boolean;
  onCreated: (e: any) => void;
}) => {
  const map = useMap();
  const [drawControl, setDrawControl] = useState<any>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentDrawer, setCurrentDrawer] = useState<any>(null);

  useEffect(() => {
    if (!map) return;

    // Create draw control
    const drawnItems = new L.FeatureGroup();
    map.addLayer(drawnItems);

    const drawControlInstance = new L.Control.Draw({
      position: 'topright',
      draw: {
        polygon: {
          allowIntersection: false,
          drawError: {
            color: '#e1e100',
            message: '<strong>Error:</strong> Shape edges cannot cross!'
          },
          shapeOptions: {
            color: '#0a4273',
            weight: 2,
            fillOpacity: 0.2
          }
        },
        rectangle: false,
        circle: false,
        circlemarker: false,
        marker: false,
        polyline: false
      },
      edit: {
        featureGroup: drawnItems
      }
    });

    map.addControl(drawControlInstance);
    setDrawControl(drawControlInstance);

    // Listen for draw events
    map.on(L.Draw.Event.CREATED, (e: any) => {
      drawnItems.addLayer(e.layer);
      onCreated(e);
      setIsDrawing(false);
    });

    return () => {
      map.removeControl(drawControlInstance);
      map.removeLayer(drawnItems);
      map.off(L.Draw.Event.CREATED);
    };
  }, [map, onCreated]);

  useEffect(() => {
    if (!drawControl || !map) return;

    if (isDrawingMode && !isDrawing) {
      // Programmatically start polygon drawing
      const polygonDrawer = new L.Draw.Polygon(map as any, drawControl.options.draw.polygon);
      polygonDrawer.enable();
      setCurrentDrawer(polygonDrawer);
      setIsDrawing(true);
    } else if (!isDrawingMode && isDrawing && currentDrawer) {
      // Cancel current drawing
      currentDrawer.disable();
      setCurrentDrawer(null);
      setIsDrawing(false);
    }
  }, [isDrawingMode, drawControl, map, isDrawing, currentDrawer]);

  return null;
};

const MapComponent: React.FC<MapComponentProps> = ({
  selectedLayerNames,
  dateRange,
  selectedTime,
  onDrawComplete,
  isDrawingMode,
  onDrawModeChange,
  onAOIComplete,
  sidebarCollapsed,
  selectedBasemap = 'osm:osm',
  onBasemapChange,
  legendUserMode = 'simple',
  isCoordinatePinMode = false,
  onCoordinateSelected,
  onLegendUserModeChange,
  showLegendPanel = false,
  onCloseLegendPanel,
  onAOIPreview,
  highlightedBoundaries = [],
  aoiData
}) => {

  // Debug AOI data when it changes
  useEffect(() => {
    if (aoiData) {
      console.log('🔍 AOI Data received in MapComponent:', {
        type: aoiData.type,
        hasGeometry: !!aoiData.geometry,
        hasFeature: !!aoiData.feature,
        hasBounds: !!aoiData.bounds,
        geometry: aoiData.geometry ? {
          type: aoiData.geometry.type,
          coordinatesLength: (aoiData.geometry as any).coordinates?.length
        } : null,
        bounds: aoiData.bounds
      });

      // Make AOI data available for debugging
      (window as any).currentAOIData = aoiData;

      // Summary of AOI clipping capabilities
      console.log('🎯 AOI Clipping Summary:', {
        canUseCQLFilter: !!(aoiData.geometry && aoiData.feature),
        canUseBBOXFilter: !!aoiData.bounds,
        willUseVisualClipping: true,
        selectedLayers: selectedLayerNames.length,
        temporalData: {
          hasDateRange: !!(dateRange.startDate && dateRange.endDate),
          hasSelectedTime: !!selectedTime
        }
      });
    } else {
      console.log('❌ No AOI data received in MapComponent - layers will render without clipping');
      (window as any).currentAOIData = null;
    }
  }, [aoiData, selectedLayerNames.length, dateRange, selectedTime]);

  // Add global debug function for testing
  useEffect(() => {
    (window as any).debugAOIClipping = () => {
      console.log('🔧 AOI Clipping Debug Report:');
      console.log('📍 AOI Data:', aoiData);
      console.log('📅 Date Range:', dateRange);
      console.log('🗂️ Selected Layers:', selectedLayerNames);
      console.log('🕐 Selected Time:', selectedTime);

      if (aoiData) {
        console.log('✅ AOI clipping should be active');
        if (aoiData.geometry) {
          console.log('✅ CQL filtering available');
        } else if (aoiData.bounds) {
          console.log('⚠️ Only BBOX filtering available');
        }
      } else {
        console.log('❌ No AOI clipping - layers will render normally');
      }
    };

    return () => {
      delete (window as any).debugAOIClipping;
    };
  }, [aoiData, dateRange, selectedLayerNames, selectedTime]);
  const [center] = useState<[number, number]>([-29.0, 24.0]);
  const [zoom] = useState<number>(6);
  const [wmsLayers, setWmsLayers] = useState<WMSLayer[]>([]);
  const [mapBounds, setMapBounds] = useState<any>(null);
  const [loadingLayers, setLoadingLayers] = useState<{ [key: string]: boolean }>({});
  const [layerProgress, setLayerProgress] = useState<{ [key: string]: number }>({});
  const [floodRiskAreas, setFloodRiskAreas] = useState<any[]>([]);
  const [dwsVillages, setDwsVillages] = useState<any[]>([]);
  const [activePopup, setActivePopup] = useState<string | null>(null);
  const [errorLayers, setErrorLayers] = useState<{ [key: string]: string }>({});
  const [pinCoordinates, setPinCoordinates] = useState<{ lat: number, lng: number } | null>(null);
  const [showPinAreaModal, setShowPinAreaModal] = useState(false);
  
  // Debug: Log when isCoordinatePinMode changes
  useEffect(() => {
    console.log('MapComponent: isCoordinatePinMode changed to:', isCoordinatePinMode);
  }, [isCoordinatePinMode]);
  
  // Reset pin coordinates when exiting coordinate pin mode
  useEffect(() => {
    if (!isCoordinatePinMode) {
      setPinCoordinates(null);
      setShowPinAreaModal(false);
    }
  }, [isCoordinatePinMode]);

  const [layerOpacities, setLayerOpacities] = useState<{ [layerName: string]: number }>({});

  // Legend selection state
  const [selectedLegendLayer, setSelectedLegendLayer] = useState<string | undefined>();

  // Auto-select the last selected layer when layers change
  useEffect(() => {

    // Check for exact matches
    const matchingLayers = wmsLayers.filter(wmsLayer =>
      selectedLayerNames.includes(wmsLayer.name)
    );

    // Run comprehensive debug summary (only when layers actually change)
    if (selectedLayerNames.length > 0 && wmsLayers.length > 0) {
      debugLayerMatching();
    }

    if (selectedLayerNames.length > 0 && !selectedLegendLayer) {
      // Select the last layer in the list (most recently added)
      setSelectedLegendLayer(selectedLayerNames[selectedLayerNames.length - 1]);
    } else if (selectedLayerNames.length === 0) {
      // Clear selection if no layers are visible
      setSelectedLegendLayer(undefined);
    } else if (selectedLegendLayer && !selectedLayerNames.includes(selectedLegendLayer)) {
      // If selected layer is no longer visible, select the last visible layer
      setSelectedLegendLayer(selectedLayerNames[selectedLayerNames.length - 1]);
    }
  }, [selectedLayerNames, wmsLayers]); // Removed selectedLegendLayer and debugLayerMatching from dependencies

  // Handle manual legend layer selection
  const handleLegendLayerSelect = (layerName: string) => {
    setSelectedLegendLayer(layerName);
  };

  // Feature info popup state
  const [featureInfo, setFeatureInfo] = useState<FeatureInfo | null>(null);

  // AOI state management
  const [drawnPolygon, setDrawnPolygon] = useState<any>(null);
  const [showTemporalModal, setShowTemporalModal] = useState(false);

  // Tile error tracking to prevent infinite retries
  const [failedTiles, setFailedTiles] = useState<Set<string>>(new Set());
  const [layerErrorCounts, setLayerErrorCounts] = useState<Record<string, number>>({});
  const [disabledLayers, setDisabledLayers] = useState<Set<string>>(new Set());

  // Track last toggled layer for expanded legend (now using selectedLegendLayer)
  const [lastToggledLayer, setLastToggledLayer] = useState<WMSLayer | null>(null);

  // Update lastToggledLayer when selectedLegendLayer changes
  useEffect(() => {
    if (selectedLegendLayer) {
      const selectedLayer = wmsLayers.find(layer => layer.name === selectedLegendLayer);
      if (selectedLayer) {
        setLastToggledLayer(selectedLayer);
      }
    }
  }, [selectedLegendLayer, wmsLayers]);
  const [popupPosition, setPopupPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  // Removed isSentinelLayer - using dynamic layer properties instead

  const getLayerBounds = useCallback((bbox: any) => {
    if (!bbox) return undefined;

    const { miny, minx, maxy, maxx } = bbox;

    // Convert to numbers and validate
    const minY = Number(miny);
    const minX = Number(minx);
    const maxY = Number(maxy);
    const maxX = Number(maxx);

    // Check for invalid coordinates
    if (isNaN(minY) || isNaN(minX) || isNaN(maxY) || isNaN(maxX)) {
      console.warn('Invalid bbox coordinates detected:', { miny, minx, maxy, maxx });
      return undefined; // Let Leaflet use default bounds
    }

    // Validate coordinate ranges
    if (minY < -90 || minY > 90 || maxY < -90 || maxY > 90 ||
        minX < -180 || minX > 180 || maxX < -180 || maxX > 180) {
      console.warn('Bbox coordinates out of valid range:', { minY, minX, maxY, maxX });
      return undefined; // Let Leaflet use default bounds
    }

    // Ensure min < max
    if (minY >= maxY || minX >= maxX) {
      console.warn('Invalid bbox: min >= max:', { minY, minX, maxY, maxX });
      return undefined; // Let Leaflet use default bounds
    }

    return [[minY, minX], [maxY, maxX]] as [[number, number], [number, number]];
  }, []);

  const isLayerVisible = useCallback((layer: WMSLayer) => {
    if (!layer || !layer.name) {
      return false;
    }

    // Simple check: layer is visible if it's in the selected layers array
    // Some layer names might include namespace prefixes that need to be stripped
    const layerBaseName = layer.name.includes(':') ? layer.name.split(':')[1] : layer.name;
    const isVisible = selectedLayerNames.includes(layer.name) || selectedLayerNames.includes(layerBaseName);

    return isVisible;
  }, [selectedLayerNames]);



  const handleLayerOpacityChange = useCallback((layerName: string, newOpacity: number) => {
    setLayerOpacities(prev => ({
      ...prev,
      [layerName]: newOpacity,
    }));
  }, []);

  // Cleanup opacity state when layers are removed
  useEffect(() => {
    const visibleLayerNames = wmsLayers.filter(isLayerVisible).map(layer => layer.name);
    setLayerOpacities(prev => {
      const cleaned = { ...prev };
      Object.keys(cleaned).forEach(layerName => {
        if (!visibleLayerNames.includes(layerName)) {
          delete cleaned[layerName];
        }
      });
      return cleaned;
    });
    
    // Clear error tracking for layers that are no longer visible
    setLayerErrorCounts(prev => {
      const newCounts = { ...prev };
      Object.keys(newCounts).forEach(layerName => {
        if (!visibleLayerNames.includes(layerName)) {
          delete newCounts[layerName];
        }
      });
      return newCounts;
    });
    
    // Clear failed tiles for layers that are no longer visible
    setFailedTiles(prev => {
      const newSet = new Set<string>();
      prev.forEach(tileKey => {
        const layerName = tileKey.split('-')[0];
        if (visibleLayerNames.includes(layerName)) {
          newSet.add(tileKey);
        }
      });
      return newSet;
    });
  }, [wmsLayers]); // Removed isLayerVisible from dependencies to prevent re-renders

  useEffect(() => {
    const loadAllLayers = async () => {
      try {
        // Load layers from discovery service (includes both local and remote)
        const discoveryResult = await discoverLayers();
        const allLayers = discoveryResult.layers || [];

        // Convert discovery layers to WMSLayer format for compatibility
        const wmsCompatibleLayers = allLayers.map(layer => {
          // Handle different bbox formats
          let bbox;
          const layerBbox = (layer as any).bbox;

          if (layerBbox && Array.isArray(layerBbox) && layerBbox.length === 4) {
            // Array format: [minx, miny, maxx, maxy]
            bbox = {
              minx: layerBbox[0],
              miny: layerBbox[1],
              maxx: layerBbox[2],
              maxy: layerBbox[3]
            };
          } else if (layerBbox && typeof layerBbox === 'object') {
            // Object format: {minx, miny, maxx, maxy}
            bbox = {
              minx: layerBbox.minx || layerBbox[0] || -180,
              miny: layerBbox.miny || layerBbox[1] || -90,
              maxx: layerBbox.maxx || layerBbox[2] || 180,
              maxy: layerBbox.maxy || layerBbox[3] || 90
            };
          } else {
            // Default global bounds
            bbox = {
              minx: -180,
              miny: -90,
              maxx: 180,
              maxy: 90
            };
          }

          // Validate bbox values
          if (isNaN(bbox.minx) || isNaN(bbox.miny) || isNaN(bbox.maxx) || isNaN(bbox.maxy)) {
            console.warn(`Invalid bbox for layer ${layer.name}, using global bounds`);
            bbox = { minx: -180, miny: -90, maxx: 180, maxy: 90 };
          }

          return {
            name: layer.name,
            title: layer.title || layer.name,
            type: 'raster',
            queryable: (layer as any).queryable || false,
            bbox: bbox,
            // Add remote layer properties
            isRemote: (layer as any).isRemote || false,
            serviceType: (layer as any).serviceType,
            remoteUrl: (layer as any).remoteUrl,
            url: (layer as any).url
          };
        });


        setWmsLayers(wmsCompatibleLayers as WMSLayer[]);
      } catch (error) {
        console.error('MapComponent: Failed to load layers from discovery:', error);

        // Fallback to local WMS layers only
        try {
          const localLayers = await fetchAvailableWMSLayers();
          setWmsLayers(localLayers);
        } catch (fallbackError) {
          console.error('MapComponent: Fallback also failed:', fallbackError);
          setWmsLayers([]);
        }
      }
    };
    loadAllLayers();
  }, []);

  useEffect(() => {
    if (selectedLayerNames.includes('floodRisk') && mapBounds) {
      fetchFloodRiskData(mapBounds, dateRange)
        .then(setFloodRiskAreas)
        .catch(console.error);
    } else {
      setFloodRiskAreas([]);
    }
  }, [selectedLayerNames, dateRange, mapBounds]);

  useEffect(() => {
    if (selectedLayerNames.includes('dwsVillage') && mapBounds) {
      fetchLayerData('dwsVillage', mapBounds)
        .then(setDwsVillages)
        .catch(console.error);
    } else {
      setDwsVillages([]);
    }
  }, [selectedLayerNames, mapBounds]);

  // Track the last toggled layer for expanded legend
  useEffect(() => {
    
    const visibleLayers = wmsLayers.filter(layer => selectedLayerNames.includes(layer.name));
    if (visibleLayers.length > 0) {
      // Set the last layer in the visible layers array as the last toggled
      setLastToggledLayer(visibleLayers[visibleLayers.length - 1]);
    } else {
      setLastToggledLayer(null);
    }
  }, [selectedLayerNames, wmsLayers]);

  const handleCreated = useCallback((e: any) => {
    if (e.layerType === 'polygon') {
      const geoJSON = e.layer.toGeoJSON();

      // If in AOI drawing mode, start the AOI workflow
      if (isDrawingMode) {
        setDrawnPolygon(geoJSON);
        onDrawModeChange(false); // Exit drawing mode
        setShowTemporalModal(true); // Show temporal selection modal
      } else {
        // Regular drawing functionality
        onDrawComplete(geoJSON);
      }
    }
  }, [onDrawComplete, isDrawingMode, onDrawModeChange]);

  // Handle feature info click
  const handleFeatureInfoClick = useCallback(async (event: any, queryableLayers: WMSLayer[]) => {
    const { latlng, containerPoint } = event;

    // Set popup position based on click coordinates
    setPopupPosition({ x: containerPoint.x + 10, y: containerPoint.y - 10 });

    try {
      // For now, query the first queryable layer
      // TODO: In the future, we could query multiple layers and combine results
      const layer = queryableLayers[0];

      // Get map container to calculate dimensions
      const mapContainer = event.target.getContainer();
      const mapSize = event.target.getSize();
      const bounds = event.target.getBounds();

      const params: GetFeatureInfoParams = {
        layers: layer.name,
        queryLayers: layer.name,
        x: Math.round(containerPoint.x),
        y: Math.round(containerPoint.y),
        width: mapSize.x,
        height: mapSize.y,
        bbox: `${bounds.getWest()},${bounds.getSouth()},${bounds.getEast()},${bounds.getNorth()}`,
        srs: 'EPSG:4326',
        infoFormat: 'application/json',
        featureCount: 10
      };

      const response = await fetchFeatureInfo(params);

      // Parse GeoServer response (format may vary)
      let features = [];
      if (response && response.features) {
        features = response.features;
      } else if (response && Array.isArray(response)) {
        features = response;
      } else if (response && typeof response === 'object') {
        // Handle other response formats
        features = [{ properties: response }];
      }
      
      if (features.length > 0) {
        setFeatureInfo({
          layerName: layer.title || layer.name,
          features: features,
          coordinates: {
            lat: latlng.lat,
            lng: latlng.lng
          }
        });
      }
    } catch (error) {
      console.error('Error fetching feature info:', error);
      // Could show an error message to user here
    }
  }, []);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'red';
      case 'moderate': return 'yellow';
      case 'low': return 'green';
      default: return 'blue';
    }
  };

  const handleMarkerClick = useCallback((layerName: string) => {
    setActivePopup(prev => prev === layerName ? null : layerName);
  }, []);

  const handleLayerLoading = useCallback((layerName: string) => {
    setLoadingLayers(prev => ({ ...prev, [layerName]: true }));
    setLayerProgress(prev => ({ ...prev, [layerName]: 0 }));
  }, []);

  const handleLayerLoad = useCallback((layerName: string) => {
    setLoadingLayers(prev => ({ ...prev, [layerName]: false }));
    setLayerProgress(prev => ({ ...prev, [layerName]: 100 }));
    setErrorLayers(prev => {
      const updated = { ...prev };
      delete updated[layerName];
      return updated;
    });

    // Clear the completed loading state after a short delay to prevent lingering
    setTimeout(() => {
      setLayerProgress(prev => {
        const updated = { ...prev };
        delete updated[layerName];
        return updated;
      });
    }, 2000);
  }, []);

  const handleLayerError = useCallback((layerName: string, error: any) => {
    // Increment error count for this layer
    setLayerErrorCounts(prev => {
      const newCount = (prev[layerName] || 0) + 1;
      const updatedCounts = { ...prev, [layerName]: newCount };
      
      // If this layer has had too many errors, disable it and stop retries
      if (newCount >= 10) {
        console.error(`🚫 Layer "${layerName}" has failed ${newCount} times - disabling to prevent infinite loops`);
        setDisabledLayers(prevDisabled => new Set(prevDisabled).add(layerName));
        setErrorLayers(prev => ({ ...prev, [layerName]: 'Layer disabled due to repeated failures' }));
        return updatedCounts;
      }
      
      // If this layer has had too many errors, stop reporting to prevent spam
      if (newCount > 5) {
        console.warn(`⚠️ Layer "${layerName}" has failed ${newCount} times - throttling error reporting`);
        return updatedCounts;
      }
      
      console.error(`❌ Layer error for "${layerName}" (attempt ${newCount}):`, error);
      return updatedCounts;
    });
    
    setLoadingLayers(prev => ({ ...prev, [layerName]: false }));
    setErrorLayers(prev => ({ ...prev, [layerName]: error.message || 'Failed to load layer' }));
  }, []);

  // Debug summary function
  // Function to convert date range to WMS TIME parameter format
  const formatTimeParameter = useCallback((dateRange: { startDate: string; endDate: string }) => {
    if (!dateRange.startDate || !dateRange.endDate) {
      return undefined;
    }
    
    // Convert to ISO 8601 format for WMS TIME parameter
    const startISO = new Date(dateRange.startDate).toISOString().split('T')[0];
    const endISO = new Date(dateRange.endDate).toISOString().split('T')[0];
    
    // WMS TIME parameter format: start/end for ranges
    return `${startISO}/${endISO}`;
  }, []);

  // Get the time parameter for temporal layers
  const getTimeParameter = useCallback(() => {
    // Use selectedTime if provided, otherwise use date range
    if (selectedTime) {
      return selectedTime;
    }
    
    return formatTimeParameter(dateRange);
  }, [selectedTime, dateRange, formatTimeParameter]);

  // Effect to refresh layers when date range changes (Phase 3 implementation)
  useEffect(() => {
    // Calculate time parameter directly to avoid dependency issues
    const timeParam = selectedTime || formatTimeParameter(dateRange);
    if (timeParam && selectedLayerNames.length > 0) {
      console.log(`🔄 Date range changed, refreshing temporal layers with TIME: ${timeParam}`);
      
      // Force layer refresh by clearing and reloading layer progress
      setLayerProgress(prev => {
        const newProgress = { ...prev };
        selectedLayerNames.forEach(layerName => {
          newProgress[layerName] = 0;
        });
        return newProgress;
      });
    }
  }, [dateRange, selectedTime, selectedLayerNames, formatTimeParameter]);

  const debugLayerMatching = useCallback(() => {
    const baseWmsUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy`;

    // Show example URLs for selected layers
    const visibleLayers = wmsLayers.filter(layer => isLayerVisible(layer));
    if (visibleLayers.length > 0) {
      visibleLayers.forEach(layer => {
        const exampleParams = new URLSearchParams({
          SERVICE: 'WMS',
          VERSION: '1.1.1',
          REQUEST: 'GetMap',
          LAYERS: layer.name,
          STYLES: '',
          FORMAT: 'image/png',
          TRANSPARENT: 'true',
          SRS: 'EPSG:4326',
          BBOX: '-180,-90,180,90',
          WIDTH: '256',
          HEIGHT: '256'
        });
        const fullUrl = `${baseWmsUrl}?${exampleParams.toString()}`;
        console.log(`  ${layer.name}: ${fullUrl}`);
      });
    }
  }, [selectedLayerNames, wmsLayers, isLayerVisible, errorLayers, loadingLayers]);

  // Function to test WMS URL accessibility (call from console)
  const testWmsUrl = useCallback(async (layerName: string) => {
    const baseUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy`;
    const testParams = new URLSearchParams({
      SERVICE: 'WMS',
      VERSION: '1.1.1',
      REQUEST: 'GetMap',
      LAYERS: layerName,
      STYLES: '',
      FORMAT: 'image/png',
      TRANSPARENT: 'true',
      SRS: 'EPSG:4326',
      BBOX: '-180,-90,180,90',
      WIDTH: '256',
      HEIGHT: '256'
    });
    const testUrl = `${baseUrl}?${testParams.toString()}`;


    try {
      const response = await fetch(testUrl);
      if (!response.ok) {
        const errorText = await response.text();
        console.error(` WMS Error Response:`, errorText);
      }
    } catch (error) {
      console.error(` WMS URL test failed:`, error);
    }
  }, []);

  // Make testWmsUrl available globally for console debugging
  useEffect(() => {
    (window as any).testWmsUrl = testWmsUrl;
    (window as any).debugLayerMatching = debugLayerMatching;
    // Initialize AOI debugger
    console.log('🛠️ AOI Debugging tools available:');
    console.log('  - window.aoiDebugger.runFullTest() - Run comprehensive tests');
    console.log('  - window.aoiDebugger.testCurrentAOI() - Test current AOI state');
    console.log('  - window.testWmsUrl(layerName) - Test WMS layer accessibility');
    console.log('  - window.debugLayerMatching() - Debug layer matching');

    return () => {
      delete (window as any).testWmsUrl;
      delete (window as any).debugLayerMatching;
    };
  }, [testWmsUrl, debugLayerMatching]);

  const handleCloseLoader = useCallback(() => {
    setLoadingLayers({});
    setErrorLayers({});
    setLayerProgress({});
  }, []);

  // AOI workflow handlers
  const handleTemporalConfirm = useCallback((dateRange: { startDate: string; endDate: string }) => {
    setShowTemporalModal(false);
    
    // Instead of showing modal, call the AOI preview callback
    if (onAOIPreview && drawnPolygon) {
      const coordinates = drawnPolygon.geometry.coordinates[0];
      const lats = coordinates.map((coord: number[]) => coord[1]);
      const lngs = coordinates.map((coord: number[]) => coord[0]);

      const bounds = {
        north: Math.max(...lats),
        south: Math.min(...lats),
        east: Math.max(...lngs),
        west: Math.min(...lngs)
      };

      // Calculate area (simple bounding box area)
      const area = Math.abs((bounds.east - bounds.west) * (bounds.north - bounds.south)) * 111 * 111;

      const aoiData = {
        type: 'drawn',
        coordinates: drawnPolygon,
        bounds,
        area,
        dateRange
      };

      onAOIPreview(aoiData);
    }
  }, [drawnPolygon, onAOIPreview]);

  // Pin area selection handlers
  const handlePinPlaced = useCallback((latlng: { lat: number, lng: number }) => {
    console.log('Pin placed at:', latlng);
    setShowPinAreaModal(true);
  }, []);

  const handlePinAreaSelected = useCallback((areaConfig: any) => {
    console.log('Pin area selected:', areaConfig);
    setShowPinAreaModal(false);
    
    // Create AOI data similar to drawn polygon
    if (onAOIPreview) {
      const aoiData = {
        type: 'pin',
        name: `Pin Area (${areaConfig.size} km²)`,
        coordinates: areaConfig.coordinates,
        bounds: areaConfig.bounds,
        area: areaConfig.area,
        shape: areaConfig.shape,
        dateRange: null // Will be handled in temporal selection if needed
      };

      onAOIPreview(aoiData);
    }
  }, [onAOIPreview]);

  return (
    <div className="map-wrapper">
      <MapContainer
        center={center}
        zoom={zoom}
        scrollWheelZoom
        className={`map ${isDrawingMode ? 'drawing-mode' : ''} ${isCoordinatePinMode ? 'pin-mode' : ''}`}
        worldCopyJump
        maxBoundsViscosity={1.0}
      >
        <MapRecentre trigger={selectedLayerNames.length === 0} center={center} zoom={zoom} />
        {/* Dynamic Basemap Layer */}
        {selectedBasemap === 'osm:osm' ? (
          <TileLayer
            attribution='&copy; OpenStreetMap contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
        ) : (
          <WMSTileLayer
            key={`basemap-${selectedBasemap}`}
            url={`${API_CONFIG.BASE_URL}/ows/wms-proxy`}
            layers={selectedBasemap}
            format="image/jpeg"
            transparent={false}
            version="1.1.1"
            attribution="GeoServer Basemap"
            opacity={1}
            maxZoom={19}
            minZoom={2}
            tileSize={256}
          />
        )}
        <LayerGroup>
          {/* Show warnings for selected layers that can't be found */}
          {selectedLayerNames.length > 0 && (
            <div className="layer-warnings" style={{ 
              position: 'absolute', 
              zIndex: 1000, 
              top: 10, 
              right: 10, 
              maxWidth: '300px', 
              pointerEvents: 'none' 
            }}>
              {selectedLayerNames.filter(name => !wmsLayers.some(layer => 
                layer.name === name || (layer.name.includes(':') && layer.name.split(':')[1] === name)
              )).map(name => (
                <div key={`warning-${name}`} style={{ 
                  backgroundColor: 'rgba(255, 193, 7, 0.8)', 
                  color: '#212529', 
                  padding: '5px 10px',
                  borderRadius: '4px', 
                  marginBottom: '5px',
                  fontSize: '12px',
                  fontWeight: 'bold'
                }}>
                  Layer not found: {name}
                </div>
              ))}
              {/* Show warnings for disabled layers */}
              {selectedLayerNames.filter(name => disabledLayers.has(name)).map(name => (
                <div key={`disabled-${name}`} style={{ 
                  backgroundColor: 'rgba(220, 53, 69, 0.8)', 
                  color: '#fff', 
                  padding: '5px 10px',
                  borderRadius: '4px', 
                  marginBottom: '5px',
                  fontSize: '12px',
                  fontWeight: 'bold'
                }}>
                  🚫 Layer "{name}" disabled (too many errors)
                </div>
              ))}
            </div>
          )}
          {wmsLayers.filter(layer => {
            const visible = isLayerVisible(layer);
            const disabled = disabledLayers.has(layer.name);
            
            if (disabled) {
              console.warn(`🚫 Skipping disabled layer: ${layer.name}`);
              return false;
            }
            
            return visible;
          }).map(layer => {
            const isVisible = isLayerVisible(layer);

            if (!isVisible) return null;

            const bounds = getLayerBounds(layer.bbox);
            console.log(`🗺️ Layer "${layer.name}" bounds:`, bounds);

            // Skip layer if bounds are invalid
            if (bounds && bounds.some(coord => coord.some(val => isNaN(val)))) {
              console.error(` Skipping layer "${layer.name}" due to invalid bounds:`, bounds);
              return null;
            }

            // Check if this is a remote layer
            const isRemoteLayer = (layer as any).isRemote;

            // Dynamic format detection based on layer properties
            const format = (layer as any).formats?.includes('image/png') ? 'image/jpeg' : 'image/png';
            const transparent = format === 'image/png';

            // Determine the appropriate URL based on layer type
            let baseLayerUrl: string;
            let layerName: string;

            // Always use the WMS proxy for both local and remote layers
            // The proxy will handle the conversion for remote layers
            baseLayerUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy`;
            layerName = layer.name;

            // Get temporal parameter for this layer
            const timeParam = getTimeParameter();

            // Enhanced temporal parameter debugging
            const isTemporalLayer = layer.temporal || (layer as any).isTemporal;
            if (isTemporalLayer) {
              console.log(`🕐 Temporal layer "${layer.name}":`, {
                hasTimeParam: !!timeParam,
                timeParam: timeParam,
                dateRange: dateRange,
                selectedTime: selectedTime,
                layerTemporal: layer.temporal
              });
            } else if (timeParam) {
              console.log(`⚠️ Non-temporal layer "${layer.name}" has time parameter: ${timeParam}`);
            }

            // Build final layer URL with parameters
            let layerUrl = baseLayerUrl;
            
            // Add temporal and AOI clipping parameters
            const urlParams = new URLSearchParams();
            if (timeParam) {
              urlParams.set('time', timeParam);
            }
            
            // Add AOI clipping parameters
            if (aoiData) {
              console.log(`🎯 Applying AOI clipping to layer "${layer.name}":`, {
                hasGeometry: !!aoiData.geometry,
                hasFeature: !!aoiData.feature,
                hasBounds: !!aoiData.bounds,
                aoiType: aoiData.type
              });

              // Method 1: CQL filter for precise geometry-based clipping (preferred)
              if (aoiData.geometry && aoiData.feature) {
                try {
                  const geometryWKT = convertGeoJSONToWKT(aoiData.geometry);
                  const cqlFilter = `INTERSECTS(the_geom, ${geometryWKT})`;
                  urlParams.set('CQL_FILTER', cqlFilter);

                  console.log(`✂️ Adding CQL clipping filter for layer "${layer.name}": ${cqlFilter.substring(0, 100)}...`);
                } catch (error) {
                  console.warn(`❌ Failed to create CQL filter for layer "${layer.name}":`, error);

                  // Fallback to Method 2: BBOX clipping
                  if (aoiData.bounds) {
                    const bbox = `${aoiData.bounds.west},${aoiData.bounds.south},${aoiData.bounds.east},${aoiData.bounds.north}`;
                    urlParams.set('BBOX', bbox);
                    urlParams.set('SRS', 'EPSG:4326');
                    console.log(`📦 Fallback: Adding BBOX clipping for layer "${layer.name}": ${bbox}`);
                  }
                }
              }
              // Method 2: BBOX clipping for bounds-only AOI
              else if (aoiData.bounds) {
                const bbox = `${aoiData.bounds.west},${aoiData.bounds.south},${aoiData.bounds.east},${aoiData.bounds.north}`;
                urlParams.set('BBOX', bbox);
                urlParams.set('SRS', 'EPSG:4326');
                console.log(`📦 Adding BBOX clipping for layer "${layer.name}": ${bbox}`);
              } else {
                console.warn(`⚠️ AOI data exists but no geometry or bounds available for layer "${layer.name}"`);
              }
            }
            
            if (urlParams.toString()) {
              layerUrl += `?${urlParams.toString()}`;
            }

            // Build bounds for layer (use AOI bounds if available, otherwise layer bounds)
            let effectiveBounds = bounds;
            if (aoiData?.bounds && bounds) {
              // Use AOI bounds for clipping
              effectiveBounds = [
                [aoiData.bounds.south, aoiData.bounds.west],
                [aoiData.bounds.north, aoiData.bounds.east]
              ] as [[number, number], [number, number]];
              
              console.log(`✂️ Layer "${layer.name}" clipped to AOI bounds:`, effectiveBounds);
            }

            return (
              <WMSTileLayer
                key={`${layer.name}-${aoiData?.geometry ? 'aoi-clipped' : 'normal'}-${timeParam || 'no-time'}`} // Force re-render when AOI geometry or time changes
                url={layerUrl}
                layers={layerName}
                format={format}
                transparent={transparent}
                version="1.1.1"
                bounds={effectiveBounds}
                attribution={`${layer.title}${isRemoteLayer ? ' (Remote)' : ''}${aoiData?.geometry ? ' (AOI Clipped)' : ''}`}
                opacity={layerOpacities[layer.name] ?? 1.0}
                maxZoom={19}
                minZoom={2}
                tileSize={256}
                eventHandlers={{
                  loading: () => {
                    console.log(`🔄 Layer "${layer.name}" loading... (${isRemoteLayer ? 'Remote' : 'Local'}${aoiData ? ' with AOI clipping' : ''})`);
                    handleLayerLoading(layer.name);
                  },
                  load: () => {
                    console.log(`✅ Layer "${layer.name}" loaded successfully (${isRemoteLayer ? 'Remote' : 'Local'}${aoiData ? ' with AOI clipping' : ''})`);
                    handleLayerLoad(layer.name);
                  },
                  error: (e) => {
                    console.error(` Layer "${layer.name}" error (${isRemoteLayer ? 'Remote' : 'Local'}):`, e);
                    handleLayerError(layer.name, e);
                  },
                  tileerror: (e) => {
                    // Create a unique tile identifier to prevent duplicate error reporting
                    const tileKey = `${layer.name}-${e.coords?.x || 0}-${e.coords?.y || 0}-${e.coords?.z || 0}`;
                    
                    // Only log if this specific tile hasn't failed before
                    setFailedTiles(prev => {
                      if (prev.has(tileKey)) {
                        // Silent failure for duplicate tiles to prevent spam
                        return prev;
                      }
                      
                      const newSet = new Set(prev);
                      newSet.add(tileKey);
                      
                      // Get current error count for this layer
                      const currentCount = layerErrorCounts[layer.name] || 0;
                      if (currentCount < 3) {
                        console.error(`🧩 Layer "${layer.name}" tile error (${isRemoteLayer ? 'Remote' : 'Local'}):`, e);
                      } else if (currentCount === 3) {
                        console.warn(`⚠️ Layer "${layer.name}" - throttling tile error reports (too many failures)`);
                      }
                      
                      return newSet;
                    });
                    
                    // Only call handleLayerError occasionally to avoid overwhelming the error state
                    const currentCount = layerErrorCounts[layer.name] || 0;
                    if (currentCount < 2) {
                      handleLayerError(layer.name, { message: 'Tile load error', details: e });
                    }
                  }
                }}
              />
            );
          })}
        </LayerGroup>

        {/* AOI Clipping Overlay - Precise geometry-based clipping */}
        {aoiData?.geometry && (
          <LayerGroup>
            {/* Convert GeoJSON geometry to Leaflet-compatible coordinates */}
            {(() => {
              const geometry = aoiData.geometry;
              
              if (geometry.type === 'Polygon') {
                const coordinates = geometry.coordinates;
                
                // Create world polygon with precise AOI hole
                const worldBounds: [number, number][] = [
                  [85, -180], [85, 180], [-85, 180], [-85, -180], [85, -180]
                ];
                
                // Convert GeoJSON coordinates [lng, lat] to Leaflet [lat, lng]
                const aoiHole: [number, number][] = coordinates[0].map((coord: number[]) => [coord[1], coord[0]] as [number, number]);
                
                return (
                  <>
                    {/* Clipping mask with precise AOI hole */}
                    <Polygon
                      positions={[worldBounds, aoiHole]}
                      pathOptions={{
                        fillColor: '#000000',
                        fillOpacity: 0.3,
                        stroke: false,
                        interactive: false
                      }}
                    />
                    {/* AOI boundary outline */}
                    <Polygon
                      positions={aoiHole}
                      pathOptions={{
                        fillOpacity: 0,
                        color: '#007bff',
                        weight: 2,
                        dashArray: '5, 5',
                        interactive: false
                      }}
                    />
                  </>
                );
              } else if (geometry.type === 'MultiPolygon') {
                const worldBounds: [number, number][] = [
                  [85, -180], [85, 180], [-85, 180], [-85, -180], [85, -180]
                ];
                
                return (
                  <>
                    {/* Create clipping mask for each polygon in MultiPolygon */}
                    {geometry.coordinates.map((polygon: number[][][], index: number) => {
                      const aoiHole: [number, number][] = polygon[0].map((coord: number[]) => [coord[1], coord[0]] as [number, number]);
                      
                      return (
                        <React.Fragment key={`aoi-clip-${index}`}>
                          <Polygon
                            positions={[worldBounds, aoiHole]}
                            pathOptions={{
                              fillColor: '#000000',
                              fillOpacity: 0.3,
                              stroke: false,
                              interactive: false
                            }}
                          />
                          <Polygon
                            positions={aoiHole}
                            pathOptions={{
                              fillOpacity: 0,
                              color: '#007bff',
                              weight: 2,
                              dashArray: '5, 5',
                              interactive: false
                            }}
                          />
                        </React.Fragment>
                      );
                    })}
                  </>
                );
              }
              
              return null;
            })()}
          </LayerGroup>
        )}

        {/* Boundary Highlighting Layer for Interactive Filtering */}
        <BoundaryHighlightLayer
          features={highlightedBoundaries}
          onFeatureClick={(feature) => {
            console.log('🎯 Boundary feature clicked:', feature.properties);
          }}
        />

        <BoundsCapture onBoundsChange={setMapBounds} />
        <MapResizeHandler sidebarCollapsed={sidebarCollapsed} />
        <MapClickHandler
          wmsLayers={wmsLayers || []} // Provide empty array fallback to prevent initialization errors
          onFeatureInfoClick={handleFeatureInfoClick}
          isDrawingMode={isDrawingMode}
          isCoordinatePinMode={isCoordinatePinMode}
          onCoordinateSelected={onCoordinateSelected}
          setPinCoordinates={setPinCoordinates}
          onPinPlaced={handlePinPlaced}
        />
        <DrawingController
          isDrawingMode={isDrawingMode}
          onCreated={handleCreated}
        />
        {floodRiskAreas.map((area, i) => (
          <Polygon key={`flood-${i}`} positions={area.coordinates} pathOptions={{ color: getRiskColor(area.risk), fillOpacity: 0.4 }} />
        ))}
        {dwsVillages.map((village, i) => (
          <Marker
            key={`village-${i}`}
            position={[village.lat, village.lng]}
            eventHandlers={{ click: () => handleMarkerClick(`village-${i}`) }}
          >
            {activePopup === `village-${i}` && (
              <Popup>
                <h3>{village.name}</h3>
                <p>Population: {village.population}</p>
              </Popup>
            )}
          </Marker>
        ))}
        
        {/* Coordinate Pin Marker */}
        {isCoordinatePinMode && pinCoordinates && (
          <Marker
            key="coordinate-pin"
            position={[pinCoordinates.lat, pinCoordinates.lng]}
            icon={L.divIcon({
              className: 'coordinate-pin-marker',
              html: `<div style="background-color: #ff5722; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
              iconSize: [16, 16],
              iconAnchor: [8, 8]
            })}
          >
            <Popup>
              <div>
                <strong>Selected Coordinates</strong><br />
                Latitude: {pinCoordinates.lat.toFixed(6)}<br />
                Longitude: {pinCoordinates.lng.toFixed(6)}
              </div>
            </Popup>
          </Marker>
        )}
      </MapContainer>
      <LoadingOverlay
        loadingLayers={loadingLayers}
        wmsLayers={wmsLayers}
        errorLayers={errorLayers}
        layerProgress={layerProgress}
        onRetryLayer={() => { }} // retry implementation can be added as needed
        onCloseLoader={handleCloseLoader}
      />
      {showLegendPanel && (
        <MapLegendPanel
          visibleLayers={wmsLayers.filter(isLayerVisible)}
          mapHeight={800}
          layerOpacities={layerOpacities}
          onOpacityChange={handleLayerOpacityChange}
          userMode={legendUserMode}
          onUserModeChange={onLegendUserModeChange}
          selectedLegendLayer={selectedLegendLayer}
          onLegendLayerSelect={handleLegendLayerSelect}
          onClose={onCloseLegendPanel}
          autoShowInfo={true}
        />
      )}

      {/* Expanded Legend Panel - positioned on left, shows selected layer */}
      {lastToggledLayer && selectedLegendLayer && (
        <div className="expanded-legend-panel-container-left">
          <div className="expanded-legend-panel">
            <div className="expanded-legend-header">
              <h5 className="expanded-legend-title">
                <Layers size={18} className="me-2 header-icon" />
                Legend 
                {/* <Badge bg="light" text="dark" className="ms-2">
                  Selected
                </Badge> */}
              </h5>
            </div>
            <div className="expanded-legend-content">
              <div className="legend-content-layout">
                {lastToggledLayer.title || lastToggledLayer.name}
                <div className="original-legend-container">
                  <img
                    src={`${API_CONFIG.BASE_URL}/ows/wms-proxy?SERVICE=WMS&REQUEST=GetLegendGraphic&LAYER=${lastToggledLayer.name}&FORMAT=image/png&VERSION=1.1.1`}
                    alt={`Expanded legend for ${lastToggledLayer.name}`}
                    className="expanded-legend-image"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.onerror = null;
                      target.style.display = 'none';
                      const fallbackMsg = document.createElement('div');
                      fallbackMsg.innerText = 'Legend could not be loaded';
                      fallbackMsg.style.textAlign = 'center';
                      fallbackMsg.style.color = '#888';
                      fallbackMsg.style.padding = '20px';
                      target.parentElement?.appendChild(fallbackMsg);
                    }}
                  />
                </div>

                {/* Custom Flood Risk Legend - positioned to the right */}
                {/* <CustomFloodRiskLegend /> */}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Feature Info Popup */}
      {featureInfo && (
        <FeatureInfoPopup
          featureInfo={featureInfo}
          onClose={() => setFeatureInfo(null)}
          position={popupPosition}
        />
      )}

      {/* AOI Temporal Selection Modal */}
      <TemporalSelectionModal
        show={showTemporalModal}
        onHide={() => setShowTemporalModal(false)}
        onConfirm={handleTemporalConfirm}
        aoiCoordinates={drawnPolygon}
        preSelectedDateRange={dateRange}
      />

      {/* Pin Area Selection Modal */}
      {pinCoordinates && (
        <PinAreaSelectionModal
          show={showPinAreaModal}
          onHide={() => setShowPinAreaModal(false)}
          pinCoordinates={pinCoordinates}
          onAreaSelected={handlePinAreaSelected}
        />
      )}
    </div>
  );
};

export default MapComponent;
